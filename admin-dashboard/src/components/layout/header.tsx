import { Bell, Sun, Moon, Monitor } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAuthStore } from '@/store/auth'

import { useTheme } from '@/components/theme-provider'
import { getInitials } from '@/lib/utils'

export function Header() {
  const { user, clearAuth } = useAuthStore()

  const { theme, setTheme } = useTheme()

  const handleLogout = () => {
    clearAuth()
  }

  const themeIcon = {
    light: Sun,
    dark: Moon,
    system: Monitor,
  }[theme]

  const ThemeIcon = themeIcon

  return (
    <header className="z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-14 items-center gap-3 px-4">
        <SidebarTrigger />
        <div className="flex-1" />
        <div className="flex items-center gap-1">
          {/* Theme Toggle */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <ThemeIcon className="h-3.5 w-3.5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setTheme('light')} className="text-xs">
                <Sun className="mr-2 h-3.5 w-3.5" />
                浅色
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('dark')} className="text-xs">
                <Moon className="mr-2 h-3.5 w-3.5" />
                深色
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('system')} className="text-xs">
                <Monitor className="mr-2 h-3.5 w-3.5" />
                跟随系统
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Notifications */}
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Bell className="h-3.5 w-3.5" />
          </Button>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-7 w-7 rounded-full p-0">
                <Avatar className="h-7 w-7">
                  <AvatarImage src={user?.avatar} alt={user?.nickname} />
                  <AvatarFallback className="text-xs">
                    {user?.nickname ? getInitials(user.nickname) : 'U'}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-48" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-xs font-medium leading-none">
                    {user?.nickname}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.role === 'super_admin' ? '超级管理员' :
                     user?.role === 'admin' ? '管理员' : '用户'}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-xs">
                个人资料
              </DropdownMenuItem>
              <DropdownMenuItem className="text-xs">
                设置
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout} className="text-xs">
                退出登录
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
