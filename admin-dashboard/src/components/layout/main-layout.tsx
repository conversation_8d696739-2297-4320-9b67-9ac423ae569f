import { ReactNode } from 'react'
import { AppSidebar } from './sidebar'
import { Header } from './header'
import { AuthGuard } from '@/components/auth/auth-guard'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'

interface MainLayoutProps {
  children: ReactNode
}

function getCookieValue(name: string): string | null {
  if (typeof document === 'undefined') return null
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null
  return null
}

export function MainLayout({ children }: MainLayoutProps) {
  const defaultOpen = (() => {
    const saved = getCookieValue('sidebar_state')
    return saved === 'true'
  })()

  return (
    <AuthGuard>
      <SidebarProvider defaultOpen={defaultOpen}>
        <AppSidebar />
        <SidebarInset className="h-screen">
          <main className="flex flex-col h-full">
            <Header />
            <div className="flex-1 overflow-hidden">
              {children}
            </div>
          </main>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
