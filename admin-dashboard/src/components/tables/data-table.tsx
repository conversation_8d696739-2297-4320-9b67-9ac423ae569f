import * as React from "react"
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  flexRender,
  RowSelectionState,
  getPaginationRowModel,
  PaginationState,
} from "@tanstack/react-table"
import { useInfiniteQuery, UseInfiniteQueryResult } from "@tanstack/react-query"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { cn } from "@/lib/utils"
import {
  Search,
  RefreshCw,
  X,
} from "lucide-react"

// 筛选字段类型定义
export interface DataTableFilterField<TData = unknown> {
  label: string
  value: keyof TData
  type: 'checkbox' | 'slider' | 'input' | 'timerange'
  options?: Array<{
    label: string
    value: string
    count?: number
  }>
  min?: number
  max?: number
  defaultOpen?: boolean
  commandDisabled?: boolean
}

// 表格模式
export type TableMode = 'infinite' | 'pagination'

// 无限滚动查询函数类型
export interface InfiniteQueryFn<TData> {
  (params: {
    page: number
    pageSize: number
    search?: string
    filters?: Record<string, any>
  }): Promise<{
    data: TData[]
    total: number
    hasNextPage: boolean
  }>
}

// 分页查询函数类型
export interface PaginationQueryFn<TData> {
  (params: {
    page: number
    pageSize: number
    search?: string
    filters?: Record<string, any>
  }): Promise<{
    data: TData[]
    total: number
    totalPages: number
  }>
}

// 通用数据表格属性
export interface DataTableProps<TData> {
  // 表格模式
  mode: TableMode
  
  // 列定义
  columns: ColumnDef<TData>[]
  
  // 查询相关
  queryKey: string[]
  queryFn?: InfiniteQueryFn<TData> | PaginationQueryFn<TData>
  data?: TData[] // 静态数据模式
  
  // 分页设置
  pageSize?: number
  pagination?: PaginationState
  onPaginationChange?: (pagination: PaginationState) => void
  
  // 筛选相关
  filterFields?: DataTableFilterField<TData>[]
  enableGlobalFilter?: boolean
  
  // 选择相关
  enableRowSelection?: boolean
  rowSelection?: RowSelectionState
  onRowSelectionChange?: (selection: RowSelectionState) => void
  
  // 操作回调
  onAdd?: () => void
  onEdit?: (item: TData) => void
  onDelete?: (item: TData) => void
  onView?: (item: TData) => void
  onCopy?: (item: TData) => void
  onRefresh?: () => void
  
  // 样式相关
  className?: string
  showFilters?: boolean
  showToolbar?: boolean
  
  // 加载状态
  isLoading?: boolean
  
  // 表格尺寸
  minWidth?: string
}

export function DataTable<TData>({
  mode,
  columns,
  queryKey,
  queryFn,
  data: staticData,
  pageSize = 20,
  pagination,
  onPaginationChange,
  filterFields = [],
  enableGlobalFilter = true,
  enableRowSelection = false,
  rowSelection,
  onRowSelectionChange,
  onAdd,
  onEdit,
  onDelete,
  onView,
  onCopy,
  onRefresh,
  className,
  showFilters = true,
  showToolbar = true,
  isLoading: externalLoading,
  minWidth = '1200px',
}: DataTableProps<TData>) {
  // 状态管理
  const [internalRowSelection, setInternalRowSelection] = React.useState<RowSelectionState>({})
  const [globalFilter, setGlobalFilter] = React.useState("")
  
  // 使用外部或内部的行选择状态
  const currentRowSelection = rowSelection ?? internalRowSelection
  const setCurrentRowSelection = onRowSelectionChange ?? setInternalRowSelection

  // 数据获取逻辑
  let tableData: TData[] = []
  let totalRows = 0
  let isLoading = externalLoading || false
  let hasNextPage = false
  let fetchNextPage: (() => void) | undefined
  let isFetching = false
  let refetch: (() => void) | undefined

  if (staticData) {
    // 静态数据模式
    tableData = staticData
    totalRows = staticData.length
  } else if (mode === 'infinite' && queryFn) {
    // 无限滚动模式
    const infiniteQuery = useInfiniteQuery({
      queryKey: [...queryKey, { search: globalFilter }],
      queryFn: ({ pageParam = 1 }) => (queryFn as InfiniteQueryFn<TData>)({
        page: pageParam,
        pageSize,
        search: globalFilter,
      }),
      getNextPageParam: (lastPage, allPages) => {
        return lastPage.hasNextPage ? allPages.length + 1 : undefined
      },
      initialPageParam: 1,
    })

    tableData = infiniteQuery.data?.pages?.flatMap((page) => page.data) ?? []
    totalRows = infiniteQuery.data?.pages?.[0]?.total ?? 0
    isLoading = infiniteQuery.isLoading
    hasNextPage = infiniteQuery.hasNextPage
    fetchNextPage = infiniteQuery.fetchNextPage
    isFetching = infiniteQuery.isFetching
    refetch = infiniteQuery.refetch
  }

  // 创建表格实例
  const table = useReactTable({
    data: tableData,
    columns,
    state: {
      rowSelection: currentRowSelection,
      ...(pagination && { pagination }),
    },
    enableRowSelection,
    onRowSelectionChange: setCurrentRowSelection,
    getCoreRowModel: getCoreRowModel(),
    ...(mode === 'pagination' && {
      getPaginationRowModel: getPaginationRowModel(),
      onPaginationChange,
      manualPagination: true,
    }),
    getRowId: (row: any) => String(row.id),
  })

  // 滚动处理 - 无限加载
  const tableContainerRef = React.useRef<HTMLDivElement>(null)

  const handleScroll = React.useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      if (mode !== 'infinite' || !fetchNextPage) return
      
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
      const onPageBottom = Math.ceil(scrollTop + clientHeight) >= scrollHeight - 100

      if (onPageBottom && !isFetching && hasNextPage) {
        fetchNextPage()
      }
    },
    [fetchNextPage, isFetching, hasNextPage, mode]
  )

  return (
    <div className={cn("flex h-full bg-background", className)}>
      {/* 左侧筛选栏 */}
      {showFilters && filterFields.length > 0 && (
        <div className="w-64 border-r bg-background text-sm">
          <div className="p-3 border-b">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-sm">Filters</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {/* 重置筛选 */}}
                className="h-7 px-2 text-xs"
              >
                Reset
              </Button>
            </div>
          </div>

          <ScrollArea className="h-[calc(100%-80px)]">
            <div className="p-3">
              <div className="space-y-3">
                {filterFields.map((field) => (
                  <div key={String(field.value)} className="space-y-2">
                    <h4 className="font-medium text-xs text-muted-foreground uppercase tracking-wide">
                      {field.label}
                    </h4>
                    <div className="space-y-1">
                      {field.options?.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={option.value}
                            className="rounded border border-input w-3 h-3"
                          />
                          <label htmlFor={option.value} className="text-xs cursor-pointer">
                            {option.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </ScrollArea>
        </div>
      )}

      {/* 右侧主内容区 */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* 工具栏 */}
        {showToolbar && (
          <div className="flex items-center justify-between p-3 border-b bg-background">
            <div className="flex items-center space-x-4">
              {enableGlobalFilter && (
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search..."
                    value={globalFilter}
                    onChange={(e) => setGlobalFilter(e.target.value)}
                    className="pl-7 w-56 h-8 text-xs"
                  />
                  {globalFilter && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-1 top-1/2 h-5 w-5 -translate-y-1/2 p-0"
                      onClick={() => setGlobalFilter("")}
                    >
                      <X className="h-2.5 w-2.5" />
                    </Button>
                  )}
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <div className="text-xs text-muted-foreground">
                {tableData.length} of {totalRows} rows
              </div>
              {(onRefresh || refetch) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onRefresh?.() || refetch?.()}
                  className="h-7 px-2"
                  disabled={isLoading}
                >
                  <RefreshCw className={`h-3.5 w-3.5 ${isLoading ? 'animate-spin' : ''}`} />
                </Button>
              )}
            </div>
          </div>
        )}

        {/* 表格容器 */}
        <div className="flex-1 relative overflow-hidden">
          <div
            ref={tableContainerRef}
            className="absolute inset-0 overflow-auto"
            onScroll={handleScroll}
          >
            <Table
              className="relative text-xs"
              style={{
                minWidth,
                borderCollapse: 'separate',
                borderSpacing: 0
              }}
            >
              {/* 表头 */}
              <TableHeader className="sticky top-0 z-20 bg-background shadow-sm">
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="border-b hover:bg-transparent">
                    {headerGroup.headers.map((header) => {
                      const isSelectColumn = header.id === 'select'
                      const isActionsColumn = header.id === 'actions'

                      return (
                        <TableHead
                          key={header.id}
                          className={cn(
                            "relative border-r last:border-r-0 bg-background h-9 px-2 text-xs font-normal text-muted-foreground",
                            // 固定列样式
                            isSelectColumn && "sticky left-0 z-30 shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]",
                            isActionsColumn && "sticky right-0 z-30 shadow-[-2px_0_5px_-2px_rgba(0,0,0,0.1)]"
                          )}
                          style={{
                            width: isSelectColumn ? '50px' : isActionsColumn ? '80px' : 'auto',
                          }}
                        >
                          {header.isPlaceholder ? null : (
                            flexRender(header.column.columnDef.header, header.getContext())
                          )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>

              {/* 表体 */}
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="border-r-0 [&>:not(:last-child)]:border-r cursor-pointer hover:bg-muted/50 h-10"
                      onClick={() => {
                        // 单击打开编辑弹窗
                        if (onEdit) {
                          onEdit(row.original)
                        }
                      }}
                    >
                      {row.getVisibleCells().map((cell) => {
                        const isSelectColumn = cell.column.id === 'select'
                        const isActionsColumn = cell.column.id === 'actions'

                        return (
                          <TableCell
                            key={cell.id}
                            className={cn(
                              "border-r last:border-r-0 bg-background px-2 py-1 text-xs",
                              // 固定列样式
                              isSelectColumn && "sticky left-0 z-10 shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]",
                              isActionsColumn && "sticky right-0 z-10 shadow-[-2px_0_5px_-2px_rgba(0,0,0,0.1)]"
                            )}
                            style={{
                              width: isSelectColumn ? '50px' : isActionsColumn ? '80px' : 'auto',
                            }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        )
                      })}
                    </TableRow>
                  ))
                ) : isLoading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span className="text-muted-foreground">Loading...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      <div className="text-muted-foreground">No results.</div>
                    </TableCell>
                  </TableRow>
                )}

                {/* 无限滚动加载指示器 */}
                {mode === 'infinite' && isFetching && tableData.length > 0 && (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-12 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-3 w-3 animate-spin" />
                        <span className="text-xs text-muted-foreground">Loading more...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {/* 没有更多数据 */}
                {mode === 'infinite' && !hasNextPage && !isFetching && tableData.length > 0 && (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-12 text-center">
                      <div className="text-xs text-muted-foreground">
                        No more data to load ({tableData.length} of {totalRows} rows)
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* 分页控件 */}
        {mode === 'pagination' && pagination && onPaginationChange && (
          <div className="flex items-center justify-between p-3 border-t bg-background">
            <div className="text-sm text-muted-foreground">
              共 {totalRows} 条记录
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// 创建选择列的辅助函数
export function createSelectColumn<TData>(): ColumnDef<TData> {
  return {
    id: "select",
    header: ({ table }) => (
      <div
        className="flex items-center justify-center cursor-pointer h-full w-full"
        onClick={(e) => {
          e.stopPropagation() // 阻止事件冒泡
          table.toggleAllPageRowsSelected(!table.getIsAllPageRowsSelected())
        }}
      >
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={() => {}} // 空函数，实际由父div处理
          className="rounded border border-input pointer-events-none" // 禁用input的点击事件
        />
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="flex items-center justify-center cursor-pointer h-full w-full"
        onClick={(e) => {
          e.stopPropagation() // 阻止事件冒泡到行点击
          row.toggleSelected(!row.getIsSelected())
        }}
      >
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={() => {}} // 空函数，实际由父div处理
          className="rounded border border-input pointer-events-none" // 禁用input的点击事件
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
    enableResizing: false,
    size: 50,
    minSize: 50,
    maxSize: 50,
  }
}


