import * as React from "react"
import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { DataTable, createSelectColumn, InfiniteQueryFn, DataTableFilterField } from "./data-table"
import { Question } from "@/lib/api"
import { formatDate } from "@/lib/utils"
import { MoreHorizontal, Eye, Edit, Copy, Trash2 } from "lucide-react"

interface QuestionsTableProps {
  queryFn: InfiniteQueryFn<Question>
  queryKey: string[]
  onAdd?: () => void
  onEdit?: (question: Question) => void
  onDelete?: (question: Question) => void
  onView?: (question: Question) => void
  onCopy?: (question: Question) => void
  enableRowSelection?: boolean
}

export function QuestionsTable({
  queryFn,
  queryKey,
  onAdd,
  onEdit,
  onDelete,
  onView,
  onCopy,
  enableRowSelection = false,
}: QuestionsTableProps) {
  // 定义筛选字段
  const filterFields: DataTableFilterField<Question>[] = React.useMemo(() => [
    {
      label: "题目类型",
      value: "type",
      type: "checkbox",
      options: [
        { label: "单选题", value: "single_choice", count: 0 },
        { label: "多选题", value: "multiple_choice", count: 0 },
        { label: "判断题", value: "true_false", count: 0 },
        { label: "填空题", value: "fill_blank", count: 0 },
        { label: "简答题", value: "short_answer", count: 0 },
      ],
    },
    {
      label: "题目分类",
      value: "category",
      type: "checkbox",
      options: [
        { label: "数学", value: "math", count: 0 },
        { label: "语文", value: "chinese", count: 0 },
        { label: "英语", value: "english", count: 0 },
        { label: "物理", value: "physics", count: 0 },
        { label: "化学", value: "chemistry", count: 0 },
        { label: "生物", value: "biology", count: 0 },
        { label: "历史", value: "history", count: 0 },
        { label: "地理", value: "geography", count: 0 },
        { label: "政治", value: "politics", count: 0 },
        { label: "其他", value: "other", count: 0 },
      ],
    },
    {
      label: "难度等级",
      value: "difficulty",
      type: "checkbox",
      options: [
        { label: "简单", value: "1", count: 0 },
        { label: "中等", value: "2", count: 0 },
        { label: "困难", value: "3", count: 0 },
      ],
    },
    {
      label: "状态",
      value: "status",
      type: "checkbox",
      options: [
        { label: "启用", value: "1", count: 0 },
        { label: "禁用", value: "0", count: 0 },
      ],
    },
  ], [])

  // 定义列配置
  const columns: ColumnDef<Question>[] = React.useMemo(() => {
    const baseColumns: ColumnDef<Question>[] = [
      // ID 列
      {
        accessorKey: "id",
        header: "ID",
        cell: ({ getValue }) => (
          <div className="font-mono text-xs">
            {getValue() as number}
          </div>
        ),
        size: 60,
      },

      // 题目标题列
      {
        accessorKey: "title",
        header: "题目标题",
        cell: ({ getValue, row }) => {
          const title = getValue() as string
          const question = row.original
          return (
            <div className="max-w-[250px]">
              <div className="font-medium text-xs truncate" title={title}>
                {title}
              </div>
              <div className="text-xs text-muted-foreground truncate mt-0.5" title={question.content}>
                {question.content}
              </div>
            </div>
          )
        },
        size: 250,
        minSize: 200,
        maxSize: 350,
      },

      // 题目类型列
      {
        accessorKey: "type",
        header: "类型",
        cell: ({ getValue }) => {
          const type = getValue() as string
          const typeMap = {
            single_choice: { label: '单选题', variant: 'default' as const },
            multiple_choice: { label: '多选题', variant: 'secondary' as const },
            true_false: { label: '判断题', variant: 'outline' as const },
            fill_blank: { label: '填空题', variant: 'destructive' as const },
            short_answer: { label: '简答题', variant: 'secondary' as const },
          }
          const typeInfo = typeMap[type as keyof typeof typeMap] || { label: type, variant: 'outline' as const }
          return <Badge variant={typeInfo.variant} className="text-xs">{typeInfo.label}</Badge>
        },
        size: 100,
      },

      // 分类列
      {
        accessorKey: "category",
        header: "分类",
        cell: ({ getValue }) => {
          const category = getValue() as string
          const categoryMap = {
            math: '数学',
            chinese: '语文',
            english: '英语',
            physics: '物理',
            chemistry: '化学',
            biology: '生物',
            history: '历史',
            geography: '地理',
            politics: '政治',
            other: '其他',
          }
          return (
            <div className="text-xs">
              {categoryMap[category as keyof typeof categoryMap] || category}
            </div>
          )
        },
        size: 100,
      },

      // 难度列
      {
        accessorKey: "difficulty",
        header: "难度",
        cell: ({ getValue }) => {
          const difficulty = getValue() as number
          const difficultyMap = {
            1: { label: '简单', variant: 'default' as const },
            2: { label: '中等', variant: 'secondary' as const },
            3: { label: '困难', variant: 'destructive' as const },
          }
          const difficultyInfo = difficultyMap[difficulty as keyof typeof difficultyMap] || { label: '未知', variant: 'outline' as const }
          return <Badge variant={difficultyInfo.variant} className="text-xs">{difficultyInfo.label}</Badge>
        },
        size: 80,
      },

      // 星级列
      {
        accessorKey: "star_rating",
        header: "星级",
        cell: ({ getValue }) => {
          const rating = getValue() as number
          return (
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <span
                  key={star}
                  className={`text-xs ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
                >
                  ★
                </span>
              ))}
            </div>
          )
        },
        size: 80,
      },

      // 分值列
      {
        accessorKey: "score",
        header: "分值",
        cell: ({ getValue }) => (
          <div className="text-xs font-medium">
            {getValue() as number}分
          </div>
        ),
        size: 70,
      },

      // 选项数量列
      {
        accessorKey: "options",
        header: "选项",
        cell: ({ getValue }) => {
          const options = getValue() as string[]
          return (
            <div className="text-xs text-muted-foreground">
              {options?.length || 0}个
            </div>
          )
        },
        size: 80,
      },

      // 状态列
      {
        accessorKey: "status",
        header: "状态",
        cell: ({ getValue }) => {
          const status = getValue() as number
          return (
            <Badge variant={status === 1 ? 'default' : 'destructive'} className="text-xs">
              {status === 1 ? '启用' : '禁用'}
            </Badge>
          )
        },
        size: 80,
      },

      // 创建时间列
      {
        accessorKey: "created_at",
        header: "创建时间",
        cell: ({ getValue }) => (
          <div className="text-xs text-muted-foreground">
            {formatDate(getValue() as string)}
          </div>
        ),
        size: 100,
      },

      // 更新时间列
      {
        accessorKey: "updated_at",
        header: "更新时间",
        cell: ({ getValue }) => (
          <div className="text-xs text-muted-foreground">
            {formatDate(getValue() as string)}
          </div>
        ),
        size: 100,
      },

      // 操作列 - 固定在右侧
      {
        id: "actions",
        header: () => (
          <div className="text-center">操作</div>
        ),
        cell: ({ row }) => {
          const question = row.original

          return (
            <div className="flex items-center justify-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  {onView && (
                    <DropdownMenuItem onClick={() => onView(question)}>
                      <Eye className="mr-2 h-4 w-4" />
                      查看详情
                    </DropdownMenuItem>
                  )}
                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(question)}>
                      <Edit className="mr-2 h-4 w-4" />
                      编辑题目
                    </DropdownMenuItem>
                  )}
                  {onCopy && (
                    <DropdownMenuItem onClick={() => onCopy(question)}>
                      <Copy className="mr-2 h-4 w-4" />
                      复制题目
                    </DropdownMenuItem>
                  )}
                  {onDelete && (
                    <DropdownMenuItem
                      onClick={() => onDelete(question)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除题目
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )
        },
        enableSorting: false,
        enableHiding: false,
        enableResizing: false,
        size: 80,
        minSize: 80,
        maxSize: 80,
      },
    ]

    // 如果启用行选择，在最前面添加选择列
    if (enableRowSelection) {
      return [createSelectColumn<Question>(), ...baseColumns]
    }

    return baseColumns
  }, [
    enableRowSelection,
    onView,
    onEdit,
    onCopy,
    onDelete,
  ])

  return (
    <DataTable
      mode="infinite"
      columns={columns}
      queryKey={queryKey}
      queryFn={queryFn}
      pageSize={20}
      enableRowSelection={enableRowSelection}
      enableGlobalFilter={true}
      showFilters={true}
      filterFields={filterFields}
      showToolbar={true}
      onEdit={onEdit}
      minWidth="1600px"
      className="h-full"
    />
  )
}

export type { QuestionsTableProps }
