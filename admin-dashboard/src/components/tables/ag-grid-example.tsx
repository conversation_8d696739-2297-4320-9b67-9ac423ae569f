import * as React from "react"
import { ColumnDef } from "@tanstack/react-table"
import { AGGridStyleTable } from "./ag-grid-style-table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { MoreHorizontal, Edit, Trash2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// 示例数据类型
interface SampleData {
  id: number
  name: string
  email: string
  role: string
  status: 'active' | 'inactive'
  department: string
  salary: number
  joinDate: string
  performance: number
}

// 生成示例数据
const generateSampleData = (count: number): SampleData[] => {
  const roles = ['Developer', 'Designer', 'Manager', 'Analyst', 'QA']
  const departments = ['Engineering', 'Design', 'Marketing', 'Sales', 'HR']
  const statuses: ('active' | 'inactive')[] = ['active', 'inactive']
  
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `用户 ${i + 1}`,
    email: `user${i + 1}@example.com`,
    role: roles[Math.floor(Math.random() * roles.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    department: departments[Math.floor(Math.random() * departments.length)],
    salary: Math.floor(Math.random() * 100000) + 50000,
    joinDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
    performance: Math.floor(Math.random() * 100) + 1,
  }))
}

export function AGGridExample() {
  const [data, setData] = React.useState<SampleData[]>(() => generateSampleData(1000))
  const [editingCell, setEditingCell] = React.useState<{ rowIndex: number; columnId: string } | null>(null)

  // 可编辑单元格组件
  const EditableCell = ({ getValue, row, column, table }: any) => {
    const initialValue = getValue()
    const [value, setValue] = React.useState(initialValue)
    const [isEditing, setIsEditing] = React.useState(false)

    const onBlur = () => {
      setIsEditing(false)
      if (value !== initialValue) {
        // 更新数据
        const newData = [...data]
        newData[row.index] = { ...newData[row.index], [column.id]: value }
        setData(newData)
      }
    }

    if (isEditing) {
      return (
        <Input
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onBlur={onBlur}
          onKeyDown={(e) => {
            if (e.key === 'Enter') onBlur()
            if (e.key === 'Escape') {
              setValue(initialValue)
              setIsEditing(false)
            }
          }}
          className="h-6 text-xs border-0 p-1"
          autoFocus
        />
      )
    }

    return (
      <div
        className="h-full w-full cursor-pointer hover:bg-muted/50 p-1"
        onClick={() => setIsEditing(true)}
      >
        {value}
      </div>
    )
  }

  // 列定义
  const columns: ColumnDef<SampleData>[] = React.useMemo(() => [
    // 选择列
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      enableResizing: false,
      size: 40,
      enablePinning: true,
    },
    
    // ID列 - 固定在左侧
    {
      accessorKey: 'id',
      header: 'ID',
      size: 60,
      enablePinning: true,
      cell: ({ getValue }) => (
        <div className="font-mono text-xs">{getValue() as number}</div>
      ),
    },

    // 姓名列 - 可编辑
    {
      accessorKey: 'name',
      header: '姓名',
      size: 120,
      cell: EditableCell,
    },

    // 邮箱列 - 可编辑
    {
      accessorKey: 'email',
      header: '邮箱',
      size: 200,
      cell: EditableCell,
    },

    // 角色列
    {
      accessorKey: 'role',
      header: '角色',
      size: 100,
      cell: ({ getValue }) => {
        const role = getValue() as string
        const variants = {
          'Developer': 'default',
          'Designer': 'secondary',
          'Manager': 'destructive',
          'Analyst': 'outline',
          'QA': 'secondary',
        } as const
        return (
          <Badge variant={variants[role as keyof typeof variants] || 'default'} className="text-xs">
            {role}
          </Badge>
        )
      },
      filterFn: 'includesString',
    },

    // 状态列
    {
      accessorKey: 'status',
      header: '状态',
      size: 80,
      cell: ({ getValue }) => {
        const status = getValue() as string
        return (
          <Badge variant={status === 'active' ? 'default' : 'secondary'} className="text-xs">
            {status === 'active' ? '活跃' : '非活跃'}
          </Badge>
        )
      },
      filterFn: 'equals',
    },

    // 部门列
    {
      accessorKey: 'department',
      header: '部门',
      size: 120,
      filterFn: 'includesString',
    },

    // 薪资列 - 数字格式化
    {
      accessorKey: 'salary',
      header: '薪资',
      size: 100,
      cell: ({ getValue }) => {
        const salary = getValue() as number
        return (
          <div className="text-right font-mono text-xs">
            ¥{salary.toLocaleString()}
          </div>
        )
      },
    },

    // 入职日期列
    {
      accessorKey: 'joinDate',
      header: '入职日期',
      size: 120,
      cell: ({ getValue }) => {
        const date = getValue() as string
        return <div className="text-xs">{date}</div>
      },
    },

    // 绩效列 - 进度条
    {
      accessorKey: 'performance',
      header: '绩效',
      size: 100,
      cell: ({ getValue }) => {
        const performance = getValue() as number
        return (
          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-muted rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all"
                style={{ width: `${performance}%` }}
              />
            </div>
            <span className="text-xs w-8 text-right">{performance}%</span>
          </div>
        )
      },
    },

    // 操作列 - 固定在右侧
    {
      id: 'actions',
      header: '操作',
      size: 80,
      enableSorting: false,
      enableHiding: false,
      enableResizing: false,
      enablePinning: true,
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-6 w-6 p-0">
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Edit className="mr-2 h-3 w-3" />
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem className="text-destructive">
              <Trash2 className="mr-2 h-3 w-3" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ], [data])

  return (
    <div className="h-full p-4">
      <div className="mb-4">
        <h2 className="text-lg font-semibold">AG-Grid 风格表格示例</h2>
        <p className="text-sm text-muted-foreground">
          支持列固定、调整大小、排序、筛选、单元格编辑等功能
        </p>
      </div>

      <AGGridStyleTable
        data={data}
        columns={columns}
        enableRowSelection={true}
        enableColumnResizing={true}
        enableColumnReordering={true}
        enableSorting={true}
        enableFiltering={true}
        enablePinning={true}
        onRowClick={(row) => console.log('Row clicked:', row)}
        onCellEdit={(rowIndex, columnId, value) => {
          console.log('Cell edited:', { rowIndex, columnId, value })
        }}
        className="h-[600px]"
      />
    </div>
  )
}

export default AGGridExample
