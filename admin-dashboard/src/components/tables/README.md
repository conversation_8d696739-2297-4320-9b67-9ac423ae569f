# 通用数据表格组件

这是一个基于 TanStack Table 的通用数据表格组件，支持无限滚动、分页、筛选、行选择等功能。

## 组件结构

- `DataTable` - 通用表格组件
- `createSelectColumn` - 创建选择列的辅助函数
- `UsersTable` - 用户表格的具体实现示例

## 基本用法

### 1. 无限滚动模式

```tsx
import { DataTable, createSelectColumn } from '@/components/tables/data-table'

const columns = [
  createSelectColumn<MyDataType>(), // 可选的选择列
  {
    accessorKey: "name",
    header: "名称",
    cell: ({ getValue }) => getValue(),
  },
  // ... 更多列
]

const queryFn = async ({ page, pageSize, search }) => {
  const response = await api.getData({ page, pageSize, search })
  return {
    data: response.data,
    total: response.total,
    hasNextPage: response.hasNextPage
  }
}

<DataTable
  mode="infinite"
  columns={columns}
  queryKey={['my-data']}
  queryFn={queryFn}
  enableRowSelection={true}
  onEdit={(item) => console.log('Edit:', item)}
/>
```

### 2. 分页模式

```tsx
const queryFn = async ({ page, pageSize }) => {
  const response = await api.getData({ page, pageSize })
  return {
    data: response.data,
    total: response.total,
    totalPages: response.totalPages
  }
}

<DataTable
  mode="pagination"
  columns={columns}
  queryKey={['my-data']}
  queryFn={queryFn}
  pageSize={10}
/>
```

### 3. 静态数据模式

```tsx
<DataTable
  mode="pagination"
  columns={columns}
  data={staticData}
  enableGlobalFilter={true}
/>
```

## 主要特性

### 表格模式
- **无限滚动** (`infinite`) - 适合大量数据，滚动到底部自动加载更多
- **分页** (`pagination`) - 传统分页模式，适合需要精确页码导航的场景

### 交互功能
- **行选择** - 支持单选、多选、全选
- **行点击** - 单击行触发编辑，双击选择（可配置）
- **全局搜索** - 支持跨列搜索
- **列筛选** - 支持多种筛选类型

### 固定列
- **选择列** - 固定在左侧
- **操作列** - 固定在右侧
- **表头吸顶** - 滚动时表头保持可见

### 样式特性
- **响应式设计** - 支持水平滚动
- **紧凑布局** - 小字体、紧密间距
- **现代UI** - 使用 shadcn/ui 组件

## API 参考

### DataTable Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `mode` | `'infinite' \| 'pagination'` | - | 表格模式 |
| `columns` | `ColumnDef<TData>[]` | - | 列定义 |
| `queryKey` | `string[]` | - | React Query 查询键 |
| `queryFn` | `InfiniteQueryFn \| PaginationQueryFn` | - | 数据查询函数 |
| `data` | `TData[]` | - | 静态数据（可选） |
| `pageSize` | `number` | `20` | 每页数据量 |
| `enableRowSelection` | `boolean` | `false` | 启用行选择 |
| `enableGlobalFilter` | `boolean` | `true` | 启用全局搜索 |
| `showFilters` | `boolean` | `true` | 显示筛选栏 |
| `showToolbar` | `boolean` | `true` | 显示工具栏 |
| `onEdit` | `(item: TData) => void` | - | 编辑回调 |
| `onView` | `(item: TData) => void` | - | 查看回调 |
| `onDelete` | `(item: TData) => void` | - | 删除回调 |
| `minWidth` | `string` | `'1200px'` | 表格最小宽度 |

### 查询函数类型

```tsx
// 无限滚动查询函数
interface InfiniteQueryFn<TData> {
  (params: {
    page: number
    pageSize: number
    search?: string
    filters?: Record<string, any>
  }): Promise<{
    data: TData[]
    total: number
    hasNextPage: boolean
  }>
}

// 分页查询函数
interface PaginationQueryFn<TData> {
  (params: {
    page: number
    pageSize: number
    search?: string
    filters?: Record<string, any>
  }): Promise<{
    data: TData[]
    total: number
    totalPages: number
  }>
}
```

## 最佳实践

### 1. 列定义
- 为固定列设置合适的宽度
- 使用 `size`、`minSize`、`maxSize` 控制列宽
- 重要操作放在操作列中

### 2. 性能优化
- 使用 `React.useMemo` 包装列定义
- 合理设置 `pageSize`，避免一次加载过多数据
- 使用 React Query 的缓存机制

### 3. 用户体验
- 提供清晰的加载状态
- 合理的错误处理
- 响应式设计适配移动端

## 扩展示例

查看 `UsersTable` 组件了解如何基于 `DataTable` 创建特定业务的表格组件。
