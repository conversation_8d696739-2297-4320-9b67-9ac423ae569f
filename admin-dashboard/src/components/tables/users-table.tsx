import * as React from "react"
import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { DataTable, createSelectColumn, PaginationQueryFn } from "./data-table"
import { User } from "@/lib/api"
import { formatDate, getInitials } from "@/lib/utils"
import { MoreHorizontal, Shield, ShieldCheck, UserPlus } from "lucide-react"

interface UsersTableProps {
  queryFn: PaginationQueryFn<User>
  queryKey: string[]
  onEdit?: (user: User) => void
  onDelete?: (user: User) => void
  onView?: (user: User) => void
  onUpdateStatus?: (user: User, status: number) => void
  onPromoteToAdmin?: (user: User) => void
  onPromoteToSuperAdmin?: (user: User) => void
  onDemoteToUser?: (user: User) => void
  enableRowSelection?: boolean
}

export function UsersTable({
  queryFn,
  queryKey,
  onEdit,
  onDelete,
  onView,
  onUpdateStatus,
  onPromoteToAdmin,
  onPromoteToSuperAdmin,
  onDemoteToUser,
  enableRowSelection = false,
}: UsersTableProps) {
  // 定义列配置
  const columns: ColumnDef<User>[] = React.useMemo(() => {
    const baseColumns: ColumnDef<User>[] = [
      // ID 列
      {
        accessorKey: "id",
        header: "ID",
        cell: ({ getValue }) => (
          <div className="font-mono text-xs">
            {getValue() as number}
          </div>
        ),
        size: 60,
      },

      // 用户信息列
      {
        accessorKey: "nickname",
        header: "用户信息",
        cell: ({ row }) => {
          const user = row.original
          return (
            <div className="flex items-center space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user.avatar} />
                <AvatarFallback>
                  {getInitials(user.nickname)}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium text-xs">{user.nickname}</div>
                <div className="text-xs text-muted-foreground">
                  {user.open_id}
                </div>
              </div>
            </div>
          )
        },
        size: 200,
        minSize: 150,
        maxSize: 300,
      },

      // 角色列
      {
        accessorKey: "role",
        header: "角色",
        cell: ({ getValue }) => {
          const role = getValue() as string
          const roleMap = {
            super_admin: { label: '超级管理员', variant: 'destructive' as const },
            admin: { label: '管理员', variant: 'secondary' as const },
            user: { label: '普通用户', variant: 'outline' as const },
          }
          const roleInfo = roleMap[role as keyof typeof roleMap] || roleMap.user
          return <Badge variant={roleInfo.variant} className="text-xs">{roleInfo.label}</Badge>
        },
        size: 120,
      },

      // 状态列
      {
        accessorKey: "status",
        header: "状态",
        cell: ({ getValue }) => {
          const status = getValue() as number
          return (
            <Badge variant={status === 1 ? 'default' : 'destructive'} className="text-xs">
              {status === 1 ? '正常' : '禁用'}
            </Badge>
          )
        },
        size: 80,
      },

      // 最后登录列
      {
        accessorKey: "last_login",
        header: "最后登录",
        cell: ({ getValue }) => {
          const lastLogin = getValue() as string
          return (
            <div className="text-xs text-muted-foreground">
              {lastLogin ? formatDate(lastLogin) : '从未登录'}
            </div>
          )
        },
        size: 120,
      },

      // 注册时间列
      {
        accessorKey: "created_at",
        header: "注册时间",
        cell: ({ getValue }) => (
          <div className="text-xs text-muted-foreground">
            {formatDate(getValue() as string)}
          </div>
        ),
        size: 120,
      },

      // 操作列 - 固定在右侧
      {
        id: "actions",
        header: () => (
          <div className="text-center">操作</div>
        ),
        cell: ({ row }) => {
          const user = row.original

          return (
            <div className="flex items-center justify-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  {onView && (
                    <DropdownMenuItem onClick={() => onView(user)}>
                      查看详情
                    </DropdownMenuItem>
                  )}
                  {onUpdateStatus && (
                    <DropdownMenuItem
                      onClick={() => onUpdateStatus(user, user.status === 1 ? 0 : 1)}
                    >
                      {user.status === 1 ? '禁用用户' : '启用用户'}
                    </DropdownMenuItem>
                  )}
                  {user.role === 'user' && onPromoteToAdmin && (
                    <DropdownMenuItem onClick={() => onPromoteToAdmin(user)}>
                      <Shield className="mr-2 h-4 w-4" />
                      提升为管理员
                    </DropdownMenuItem>
                  )}
                  {user.role === 'admin' && onPromoteToSuperAdmin && (
                    <DropdownMenuItem onClick={() => onPromoteToSuperAdmin(user)}>
                      <ShieldCheck className="mr-2 h-4 w-4" />
                      提升为超级管理员
                    </DropdownMenuItem>
                  )}
                  {user.role !== 'user' && onDemoteToUser && (
                    <DropdownMenuItem onClick={() => onDemoteToUser(user)}>
                      <UserPlus className="mr-2 h-4 w-4" />
                      降级为普通用户
                    </DropdownMenuItem>
                  )}
                  {onDelete && (
                    <DropdownMenuItem
                      onClick={() => onDelete(user)}
                      className="text-destructive focus:text-destructive"
                    >
                      删除用户
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )
        },
        enableSorting: false,
        enableHiding: false,
        enableResizing: false,
        size: 80,
        minSize: 80,
        maxSize: 80,
      },
    ]

    // 如果启用行选择，在最前面添加选择列
    if (enableRowSelection) {
      return [createSelectColumn<User>(), ...baseColumns]
    }

    return baseColumns
  }, [
    enableRowSelection,
    onView,
    onUpdateStatus,
    onPromoteToAdmin,
    onPromoteToSuperAdmin,
    onDemoteToUser,
    onDelete,
  ])

  return (
    <DataTable
      mode="pagination"
      columns={columns}
      queryKey={queryKey}
      queryFn={queryFn}
      pageSize={10}
      enableRowSelection={enableRowSelection}
      enableGlobalFilter={true}
      showFilters={false}
      showToolbar={true}
      onEdit={onEdit}
      minWidth="1000px"
      className="h-full"
    />
  )
}

export type { UsersTableProps }
