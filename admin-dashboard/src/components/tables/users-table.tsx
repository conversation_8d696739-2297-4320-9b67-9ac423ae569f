import * as React from "react"
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  flexRender,
  RowSelectionState,
} from "@tanstack/react-table"
import { useInfiniteQuery } from "@tanstack/react-query"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { User } from "@/lib/api"
import { formatDate, getInitials } from "@/lib/utils"

// 筛选字段类型定义
export interface DataTableFilterField<TData = unknown> {
  label: string
  value: keyof TData
  type: 'checkbox' | 'slider' | 'input' | 'timerange'
  options?: Array<{
    label: string
    value: string
    count?: number
  }>
  min?: number
  max?: number
  defaultOpen?: boolean
  commandDisabled?: boolean
}

import {
  Edit,
  Trash2,
  Eye,
  Copy,
  Star,
  MoreHorizontal,
  ArrowUpDown,
  SortAsc,
  SortDesc,
  AlertCircle,
  Search,
  RefreshCw,
  X,
  Shield,
  ShieldCheck,
  UserPlus,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

interface UsersTableProps {
  // 无限加载查询函数
  queryFn: (params: {
    page: number
    pageSize: number
    search?: string
    filters?: Record<string, any>
  }) => Promise<{
    data: User[]
    total: number
    hasNextPage: boolean
  }>
  queryKey: string[]
  pageSize?: number
  onAdd?: () => void
  onEdit?: (user: User) => void
  onDelete?: (user: User) => void
  onView?: (user: User) => void
  onUpdateStatus?: (user: User, status: number) => void
  onPromoteToAdmin?: (user: User) => void
  onPromoteToSuperAdmin?: (user: User) => void
  onDemoteToUser?: (user: User) => void
  onRefresh?: () => void
}

// 列头组件
function DataTableColumnHeader({ 
  column, 
  title, 
  className 
}: { 
  column: any
  title: string
  className?: string 
}) {
  if (!column.getCanSort()) {
    return <div className={cn(className)}>{title}</div>
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="-ml-3 h-8 data-[state=open]:bg-accent"
          >
            <span>{title}</span>
            {column.getIsSorted() === "desc" ? (
              <SortDesc className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "asc" ? (
              <SortAsc className="ml-2 h-4 w-4" />
            ) : (
              <ArrowUpDown className="ml-2 h-4 w-4" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
            <SortAsc className="mr-2 h-3 w-3 text-muted-foreground/70" />
            Asc
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
            <SortDesc className="mr-2 h-3 w-3 text-muted-foreground/70" />
            Desc
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => column.clearSorting()}>
            <AlertCircle className="mr-2 h-3 w-3 text-muted-foreground/70" />
            Clear
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export function UsersTable({
  queryFn,
  queryKey,
  pageSize = 20,
  onEdit,
  onDelete,
  onView,
  onUpdateStatus,
  onPromoteToAdmin,
  onPromoteToSuperAdmin,
  onDemoteToUser,
}: UsersTableProps) {
  // 状态管理
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const [globalFilter, setGlobalFilter] = React.useState("")

  // 无限查询
  const {
    data: infiniteData,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isLoading,
    refetch,
  } = useInfiniteQuery({
    queryKey: [...queryKey, { search: globalFilter }],
    queryFn: ({ pageParam = 1 }) => queryFn({
      page: pageParam,
      pageSize,
      search: globalFilter,
    }),
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.hasNextPage ? allPages.length + 1 : undefined
    },
    initialPageParam: 1,
  })

  // 扁平化数据
  const flatData = React.useMemo(
    () => infiniteData?.pages?.flatMap((page) => page.data) ?? [],
    [infiniteData?.pages]
  )

  // 统计信息
  const totalRows = infiniteData?.pages?.[0]?.total ?? 0

  // 筛选字段配置
  const filterFields: DataTableFilterField<User>[] = [
    {
      label: 'Role',
      value: 'role',
      type: 'checkbox',
      defaultOpen: true,
      options: [
        { label: '超级管理员', value: 'super_admin', count: 5 },
        { label: '管理员', value: 'admin', count: 15 },
        { label: '普通用户', value: 'user', count: 80 },
      ],
    },
    {
      label: 'Status',
      value: 'status',
      type: 'checkbox',
      options: [
        { label: '正常', value: '1', count: 85 },
        { label: '禁用', value: '0', count: 15 },
      ],
    },
  ]

  // 定义列配置
  const columns: ColumnDef<User>[] = [
    // 选择列 - 固定在左侧
    {
      id: "select",
      header: ({ table }) => (
        <div 
          className="flex items-center justify-center cursor-pointer h-full w-full"
          onClick={(e) => {
            e.stopPropagation() // 阻止事件冒泡
            table.toggleAllPageRowsSelected(!table.getIsAllPageRowsSelected())
          }}
        >
          <input
            type="checkbox"
            checked={table.getIsAllPageRowsSelected()}
            onChange={() => {}} // 空函数，实际由父div处理
            className="rounded border border-input pointer-events-none" // 禁用input的点击事件
          />
        </div>
      ),
      cell: ({ row }) => (
        <div 
          className="flex items-center justify-center cursor-pointer h-full w-full"
          onClick={(e) => {
            e.stopPropagation() // 阻止事件冒泡到行点击
            row.toggleSelected(!row.getIsSelected())
          }}
        >
          <input
            type="checkbox"
            checked={row.getIsSelected()}
            onChange={() => {}} // 空函数，实际由父div处理
            className="rounded border border-input pointer-events-none" // 禁用input的点击事件
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
      enableResizing: false,
      size: 50,
      minSize: 50,
      maxSize: 50,
    },

    // ID 列
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="ID" />
      ),
      cell: ({ getValue }) => (
        <div className="font-mono text-xs">
          {getValue() as number}
        </div>
      ),
      size: 60,
    },

    // 用户信息列
    {
      accessorKey: "nickname",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="用户信息" />
      ),
      cell: ({ row }) => {
        const user = row.original
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.avatar} />
              <AvatarFallback className="text-xs">
                {getInitials(user.nickname)}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium text-xs">{user.nickname}</div>
              <div className="text-xs text-muted-foreground">
                {user.open_id}
              </div>
            </div>
          </div>
        )
      },
      size: 250,
      minSize: 200,
      maxSize: 350,
    },

    // 角色列
    {
      accessorKey: "role",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="角色" />
      ),
      cell: ({ getValue }) => {
        const role = getValue() as string
        const roleMap = {
          super_admin: { label: '超级管理员', variant: 'destructive' as const },
          admin: { label: '管理员', variant: 'secondary' as const },
          user: { label: '普通用户', variant: 'outline' as const },
        }
        const roleInfo = roleMap[role as keyof typeof roleMap] || roleMap.user
        return <Badge variant={roleInfo.variant} className="text-xs">{roleInfo.label}</Badge>
      },
      size: 120,
    },

    // 状态列
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="状态" />
      ),
      cell: ({ getValue }) => {
        const status = getValue() as number
        return (
          <Badge variant={status === 1 ? 'default' : 'destructive'} className="text-xs">
            {status === 1 ? '正常' : '禁用'}
          </Badge>
        )
      },
      size: 80,
    },

    // 最后登录列
    {
      accessorKey: "last_login",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="最后登录" />
      ),
      cell: ({ getValue }) => {
        const lastLogin = getValue() as string
        return (
          <div className="text-xs text-muted-foreground">
            {lastLogin ? formatDate(lastLogin) : '从未登录'}
          </div>
        )
      },
      size: 120,
    },

    // 注册时间列
    {
      accessorKey: "created_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="注册时间" />
      ),
      cell: ({ getValue }) => {
        const date = getValue() as string
        return (
          <div className="text-xs text-muted-foreground">
            {formatDate(date)}
          </div>
        )
      },
      size: 120,
    },

    // 操作列 - 固定在右侧
    {
      id: "actions",
      header: () => (
        <div className="text-center">Actions</div>
      ),
      cell: ({ row }) => {
        const user = row.original

        return (
          <div className="flex items-center justify-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {onView && (
                  <DropdownMenuItem onClick={() => onView(user)}>
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </DropdownMenuItem>
                )}
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(user)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                )}
                {onUpdateStatus && (
                  <DropdownMenuItem
                    onClick={() => onUpdateStatus(user, user.status === 1 ? 0 : 1)}
                  >
                    {user.status === 1 ? '禁用用户' : '启用用户'}
                  </DropdownMenuItem>
                )}
                {user.role === 'user' && onPromoteToAdmin && (
                  <DropdownMenuItem onClick={() => onPromoteToAdmin(user)}>
                    <Shield className="mr-2 h-4 w-4" />
                    提升为管理员
                  </DropdownMenuItem>
                )}
                {user.role === 'admin' && onPromoteToSuperAdmin && (
                  <DropdownMenuItem onClick={() => onPromoteToSuperAdmin(user)}>
                    <ShieldCheck className="mr-2 h-4 w-4" />
                    提升为超级管理员
                  </DropdownMenuItem>
                )}
                {user.role !== 'user' && onDemoteToUser && (
                  <DropdownMenuItem onClick={() => onDemoteToUser(user)}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    降级为普通用户
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <DropdownMenuItem
                    onClick={() => onDelete(user)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )
      },
      enableSorting: false,
      enableHiding: false,
      enableResizing: false,
      size: 80,
      minSize: 80,
      maxSize: 80,
    },
  ]

  // 创建表格实例
  const table = useReactTable({
    data: flatData,
    columns,
    state: {
      rowSelection,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getRowId: (row) => String(row.id),
  })

  // 滚动处理 - 无限加载
  const tableContainerRef = React.useRef<HTMLDivElement>(null)

  const handleScroll = React.useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
      const onPageBottom = Math.ceil(scrollTop + clientHeight) >= scrollHeight - 100

      if (onPageBottom && !isFetching && hasNextPage) {
        fetchNextPage()
      }
    },
    [fetchNextPage, isFetching, hasNextPage]
  )

  return (
    <div className="flex h-full bg-background">
      {/* 左侧筛选栏 - 固定 */}
      <div className="w-64 border-r bg-background text-sm">
        <div className="p-3 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm">Filters</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {/* 重置筛选 */}}
              className="h-7 px-2 text-xs"
            >
              Reset
            </Button>
          </div>
        </div>

        <ScrollArea className="h-[calc(100%-80px)]">
          <div className="p-3">
            <div className="space-y-3">
              {filterFields.map((field) => (
                <div key={String(field.value)} className="space-y-2">
                  <h4 className="font-medium text-xs text-muted-foreground uppercase tracking-wide">{field.label}</h4>
                  <div className="space-y-1">
                    {field.options?.map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={option.value}
                          className="rounded border border-input w-3 h-3"
                        />
                        <label htmlFor={option.value} className="text-xs cursor-pointer">
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* 右侧主内容区 */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* 工具栏 - 固定 */}
        <div className="flex items-center justify-between p-3 border-b bg-background">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="pl-7 w-56 h-8 text-xs"
              />
              {globalFilter && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 h-5 w-5 -translate-y-1/2 p-0"
                  onClick={() => setGlobalFilter("")}
                >
                  <X className="h-2.5 w-2.5" />
                </Button>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <div className="text-xs text-muted-foreground">
              {flatData.length} of {totalRows} rows
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              className="h-7 px-2"
              disabled={isLoading}
            >
              <RefreshCw className={`h-3.5 w-3.5 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* 表格容器 - 只有这部分可以滚动 */}
        <div className="flex-1 relative overflow-hidden">
          <div
            ref={tableContainerRef}
            className="absolute inset-0 overflow-auto"
            onScroll={handleScroll}
          >
            <Table
              className="relative text-xs"
              style={{
                borderCollapse: 'separate',
                borderSpacing: 0
              }}
            >
              {/* 表头 - 粘性定位 */}
              <TableHeader className="sticky top-0 z-20 bg-background shadow-sm">
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="border-b hover:bg-transparent">
                    {headerGroup.headers.map((header) => {
                      const isSelectColumn = header.id === 'select'
                      const isActionsColumn = header.id === 'actions'

                      return (
                        <TableHead
                          key={header.id}
                          className={cn(
                            "relative border-r last:border-r-0 bg-background h-9 px-2 text-xs font-normal text-muted-foreground",
                            // 固定列样式
                            isSelectColumn && "sticky left-0 z-30 shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]",
                            isActionsColumn && "sticky right-0 z-30 shadow-[-2px_0_5px_-2px_rgba(0,0,0,0.1)]"
                          )}
                          style={{
                            width: isSelectColumn ? '50px' : isActionsColumn ? '80px' : 'auto',
                          }}
                        >
                          {header.isPlaceholder ? null : (
                            flexRender(header.column.columnDef.header, header.getContext())
                          )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>

              {/* 表体 */}
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="border-r-0 [&>:not(:last-child)]:border-r cursor-pointer hover:bg-muted/50 h-10"
                      onClick={() => {
                        // 单击打开编辑弹窗
                        if (onEdit) {
                          onEdit(row.original)
                        }
                      }}
                    >
                      {row.getVisibleCells().map((cell) => {
                        const isSelectColumn = cell.column.id === 'select'
                        const isActionsColumn = cell.column.id === 'actions'

                        return (
                          <TableCell
                            key={cell.id}
                            className={cn(
                              "border-r last:border-r-0 bg-background px-2 py-1 text-xs",
                              // 固定列样式
                              isSelectColumn && "sticky left-0 z-10 shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]",
                              isActionsColumn && "sticky right-0 z-10 shadow-[-2px_0_5px_-2px_rgba(0,0,0,0.1)]"
                            )}
                            style={{
                              width: isSelectColumn ? '50px' : isActionsColumn ? '80px' : 'auto',
                            }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        )
                      })}
                    </TableRow>
                  ))
                ) : isLoading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span className="text-muted-foreground">Loading...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      <div className="text-muted-foreground">No results.</div>
                    </TableCell>
                  </TableRow>
                )}

                {/* 加载更多指示器 */}
                {isFetching && flatData.length > 0 && (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-12 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-3 w-3 animate-spin" />
                        <span className="text-xs text-muted-foreground">Loading more...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {/* 没有更多数据 */}
                {!hasNextPage && !isFetching && flatData.length > 0 && (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-12 text-center">
                      <div className="text-xs text-muted-foreground">
                        No more data to load ({flatData.length} of {totalRows} rows)
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  )
}

// 导出类型
export type { UsersTableProps }
