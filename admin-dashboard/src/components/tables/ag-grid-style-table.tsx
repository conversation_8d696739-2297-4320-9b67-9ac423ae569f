import * as React from "react"
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  flexRender,
  RowSelectionState,
  ColumnResizeMode,
  ColumnOrderState,
  VisibilityState,
  SortingState,
  getSortedRowModel,
  getFilteredRowModel,
  ColumnFiltersState,
  getPaginationRowModel,
} from "@tanstack/react-table"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu"
import { 
  ChevronDown, 
  ChevronUp, 
  MoreHorizontal, 
  <PERSON>tings,
  Filter,
  <PERSON><PERSON>p<PERSON>own,
  <PERSON>n,
  <PERSON>n<PERSON><PERSON>,
  <PERSON>,
  EyeOff,
} from "lucide-react"

interface AGGridStyleTableProps<TData> {
  data: TData[]
  columns: ColumnDef<TData>[]
  enableRowSelection?: boolean
  enableColumnResizing?: boolean
  enableColumnReordering?: boolean
  enableSorting?: boolean
  enableFiltering?: boolean
  enablePinning?: boolean
  enableVirtualization?: boolean
  onRowClick?: (row: TData) => void
  onCellEdit?: (rowIndex: number, columnId: string, value: any) => void
  className?: string
}

export function AGGridStyleTable<TData>({
  data,
  columns,
  enableRowSelection = true,
  enableColumnResizing = true,
  enableColumnReordering = true,
  enableSorting = true,
  enableFiltering = true,
  enablePinning = true,
  enableVirtualization = false,
  onRowClick,
  onCellEdit,
  className,
}: AGGridStyleTableProps<TData>) {
  // 状态管理
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const [columnOrder, setColumnOrder] = React.useState<ColumnOrderState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [globalFilter, setGlobalFilter] = React.useState("")

  // 列调整大小模式
  const columnResizeMode: ColumnResizeMode = "onChange"

  // 创建表格实例
  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection,
      columnOrder,
      columnVisibility,
      sorting,
      columnFilters,
      globalFilter,
    },
    enableRowSelection,
    enableColumnResizing,
    columnResizeMode,
    onRowSelectionChange: setRowSelection,
    onColumnOrderChange: setColumnOrder,
    onColumnVisibilityChange: setColumnVisibility,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getRowId: (row: any) => String(row.id || Math.random()),
  })

  // 列固定功能
  const handlePinColumn = (columnId: string, position: 'left' | 'right' | false) => {
    table.getColumn(columnId)?.pin(position)
  }

  // 工具栏组件
  const Toolbar = () => (
    <div className="flex items-center justify-between p-2 border-b bg-muted/30">
      <div className="flex items-center space-x-2">
        {/* 全局搜索 */}
        <Input
          placeholder="搜索..."
          value={globalFilter}
          onChange={(e) => setGlobalFilter(e.target.value)}
          className="w-64 h-8"
        />
        
        {/* 筛选器按钮 */}
        <Button variant="outline" size="sm" className="h-8">
          <Filter className="h-4 w-4 mr-1" />
          筛选器
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        {/* 列可见性控制 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-8">
              <Settings className="h-4 w-4 mr-1" />
              列设置
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {table.getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => (
                <DropdownMenuCheckboxItem
                  key={column.id}
                  className="capitalize"
                  checked={column.getIsVisible()}
                  onCheckedChange={(value) => column.toggleVisibility(!!value)}
                >
                  {column.columnDef.header as string}
                </DropdownMenuCheckboxItem>
              ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 选中行信息 */}
        {enableRowSelection && (
          <Badge variant="secondary" className="text-xs">
            已选择 {table.getFilteredSelectedRowModel().rows.length} / {table.getFilteredRowModel().rows.length} 行
          </Badge>
        )}
      </div>
    </div>
  )

  // 表头单元格组件
  const HeaderCell = ({ header }: { header: any }) => {
    const column = header.column
    const canSort = column.getCanSort()
    const canFilter = column.getCanFilter()
    const canPin = enablePinning
    const canHide = column.getCanHide()

    return (
      <ContextMenu>
        <ContextMenuTrigger asChild>
          <div
            className={cn(
              "relative flex items-center justify-between h-full px-2 py-1 text-xs font-medium bg-muted/50 border-r border-b select-none",
              canSort && "cursor-pointer hover:bg-muted/70",
              column.getIsPinned() && "bg-muted/80 shadow-sm"
            )}
            onClick={canSort ? column.getToggleSortingHandler() : undefined}
            style={{
              width: header.getSize(),
              position: column.getIsPinned() ? 'sticky' : 'relative',
              left: column.getIsPinned() === 'left' ? `${column.getStart('left')}px` : undefined,
              right: column.getIsPinned() === 'right' ? `${column.getAfter('right')}px` : undefined,
              zIndex: column.getIsPinned() ? 10 : 1,
            }}
          >
            <div className="flex items-center space-x-1 flex-1 min-w-0">
              <span className="truncate">
                {flexRender(column.columnDef.header, header.getContext())}
              </span>
              
              {/* 排序指示器 */}
              {canSort && (
                <div className="flex flex-col">
                  {column.getIsSorted() === 'asc' && <ChevronUp className="h-3 w-3" />}
                  {column.getIsSorted() === 'desc' && <ChevronDown className="h-3 w-3" />}
                  {!column.getIsSorted() && <ArrowUpDown className="h-3 w-3 opacity-50" />}
                </div>
              )}
            </div>

            {/* 列调整大小手柄 */}
            {enableColumnResizing && (
              <div
                onMouseDown={header.getResizeHandler()}
                onTouchStart={header.getResizeHandler()}
                className="absolute right-0 top-0 h-full w-1 bg-border hover:bg-primary cursor-col-resize opacity-0 hover:opacity-100 transition-opacity"
              />
            )}
          </div>
        </ContextMenuTrigger>

        {/* 右键菜单 */}
        <ContextMenuContent>
          {canSort && (
            <>
              <ContextMenuItem onClick={() => column.toggleSorting(false)}>
                升序排列
              </ContextMenuItem>
              <ContextMenuItem onClick={() => column.toggleSorting(true)}>
                降序排列
              </ContextMenuItem>
              <ContextMenuItem onClick={() => column.clearSorting()}>
                清除排序
              </ContextMenuItem>
              <DropdownMenuSeparator />
            </>
          )}
          
          {canPin && (
            <>
              <ContextMenuItem onClick={() => handlePinColumn(column.id, 'left')}>
                <Pin className="h-4 w-4 mr-2" />
                固定到左侧
              </ContextMenuItem>
              <ContextMenuItem onClick={() => handlePinColumn(column.id, 'right')}>
                <Pin className="h-4 w-4 mr-2" />
                固定到右侧
              </ContextMenuItem>
              <ContextMenuItem onClick={() => handlePinColumn(column.id, false)}>
                <PinOff className="h-4 w-4 mr-2" />
                取消固定
              </ContextMenuItem>
              <DropdownMenuSeparator />
            </>
          )}

          {canHide && (
            <ContextMenuItem onClick={() => column.toggleVisibility(false)}>
              <EyeOff className="h-4 w-4 mr-2" />
              隐藏列
            </ContextMenuItem>
          )}
        </ContextMenuContent>
      </ContextMenu>
    )
  }

  return (
    <div className={cn("flex flex-col h-full border rounded-lg overflow-hidden", className)}>
      <Toolbar />
      
      <div className="flex-1 overflow-auto">
        <div className="relative">
          <table 
            className="w-full border-collapse"
            style={{ 
              width: table.getCenterTotalSize(),
              minWidth: '100%'
            }}
          >
            {/* 表头 */}
            <thead className="sticky top-0 z-20">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <th key={header.id} className="p-0">
                      <HeaderCell header={header} />
                    </th>
                  ))}
                </tr>
              ))}
            </thead>

            {/* 表体 */}
            <tbody>
              {table.getRowModel().rows.map((row) => (
                <tr
                  key={row.id}
                  className={cn(
                    "hover:bg-muted/50 cursor-pointer transition-colors",
                    row.getIsSelected() && "bg-muted/70"
                  )}
                  onClick={() => onRowClick?.(row.original)}
                >
                  {row.getVisibleCells().map((cell) => {
                    const column = cell.column
                    return (
                      <td
                        key={cell.id}
                        className={cn(
                          "px-2 py-1 text-xs border-r border-b truncate",
                          column.getIsPinned() && "bg-background shadow-sm"
                        )}
                        style={{
                          width: cell.column.getSize(),
                          position: column.getIsPinned() ? 'sticky' : 'relative',
                          left: column.getIsPinned() === 'left' ? `${column.getStart('left')}px` : undefined,
                          right: column.getIsPinned() === 'right' ? `${column.getAfter('right')}px` : undefined,
                          zIndex: column.getIsPinned() ? 5 : 1,
                        }}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    )
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 分页控件 */}
      <div className="flex items-center justify-between p-2 border-t bg-muted/30">
        <div className="text-xs text-muted-foreground">
          显示 {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} - {Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, table.getFilteredRowModel().rows.length)} 
          / 共 {table.getFilteredRowModel().rows.length} 条
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            上一页
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            下一页
          </Button>
        </div>
      </div>
    </div>
  )
}

export type { AGGridStyleTableProps }
