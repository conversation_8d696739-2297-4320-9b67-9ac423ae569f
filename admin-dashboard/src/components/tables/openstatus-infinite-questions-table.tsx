import * as React from "react"
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  flexRender,
  RowSelectionState,
} from "@tanstack/react-table"
import { useInfiniteQuery } from "@tanstack/react-query"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Question } from "@/lib/api"

// 筛选字段类型定义
export interface DataTableFilterField<TData = unknown> {
  label: string
  value: keyof TData
  type: 'checkbox' | 'slider' | 'input' | 'timerange'
  options?: Array<{
    label: string
    value: string
    count?: number
  }>
  min?: number
  max?: number
  defaultOpen?: boolean
  commandDisabled?: boolean
}
import {
  Edit,
  Trash2,
  Eye,
  Copy,
  Star,
  MoreHorizontal,
  ArrowUpDown,
  SortAsc,
  SortDesc,
  AlertCircle,
  Search,
  RefreshCw,
  X,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

interface OpenStatusInfiniteQuestionsTableProps {
  // 无限加载查询函数
  queryFn: (params: {
    page: number
    pageSize: number
    search?: string
    filters?: Record<string, any>
  }) => Promise<{
    data: Question[]
    total: number
    hasNextPage: boolean
  }>
  queryKey: string[]
  pageSize?: number
  onAdd?: () => void
  onEdit?: (question: Question) => void
  onDelete?: (question: Question) => void
  onView?: (question: Question) => void
  onCopy?: (question: Question) => void
  onRefresh?: () => void
}

// 列头组件
function DataTableColumnHeader({ 
  column, 
  title, 
  className 
}: { 
  column: any
  title: string
  className?: string 
}) {
  if (!column.getCanSort()) {
    return <div className={cn(className)}>{title}</div>
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="-ml-3 h-8 data-[state=open]:bg-accent"
          >
            <span>{title}</span>
            {column.getIsSorted() === "desc" ? (
              <SortDesc className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "asc" ? (
              <SortAsc className="ml-2 h-4 w-4" />
            ) : (
              <ArrowUpDown className="ml-2 h-4 w-4" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
            <SortAsc className="mr-2 h-3 w-3 text-muted-foreground/70" />
            Asc
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
            <SortDesc className="mr-2 h-3 w-3 text-muted-foreground/70" />
            Desc
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => column.clearSorting()}>
            <AlertCircle className="mr-2 h-3 w-3 text-muted-foreground/70" />
            Clear
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export function OpenStatusInfiniteQuestionsTable({
  queryFn,
  queryKey,
  pageSize = 20,
  onEdit,
  onDelete,
  onView,
  onCopy,
}: OpenStatusInfiniteQuestionsTableProps) {
  // 状态管理
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const [globalFilter, setGlobalFilter] = React.useState("")

  // 无限查询
  const {
    data: infiniteData,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isLoading,
    refetch,
  } = useInfiniteQuery({
    queryKey: [...queryKey, { search: globalFilter }],
    queryFn: ({ pageParam = 1 }) => queryFn({
      page: pageParam,
      pageSize,
      search: globalFilter,
    }),
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.hasNextPage ? allPages.length + 1 : undefined
    },
    initialPageParam: 1,
  })

  // 扁平化数据
  const flatData = React.useMemo(
    () => infiniteData?.pages?.flatMap((page) => page.data) ?? [],
    [infiniteData?.pages]
  )

  // 统计信息
  const totalRows = infiniteData?.pages?.[0]?.total ?? 0

  // 筛选字段配置
  const filterFields: DataTableFilterField<Question>[] = [
    {
      label: 'Type',
      value: 'type',
      type: 'checkbox',
      defaultOpen: true,
      options: [
        { label: '单选题', value: 'single', count: 25 },
        { label: '多选题', value: 'multiple', count: 25 },
      ],
    },
    {
      label: 'Category',
      value: 'category',
      type: 'checkbox',
      defaultOpen: true,
      options: [
        { label: '数学', value: '数学', count: 10 },
        { label: '语文', value: '语文', count: 10 },
        { label: '英语', value: '英语', count: 10 },
        { label: '物理', value: '物理', count: 10 },
        { label: '化学', value: '化学', count: 10 },
      ],
    },
    {
      label: 'Difficulty',
      value: 'difficulty',
      type: 'slider',
      min: 1,
      max: 5,
      defaultOpen: true,
    },
    {
      label: 'Status',
      value: 'status',
      type: 'checkbox',
      options: [
        { label: '启用', value: '1', count: 33 },
        { label: '禁用', value: '0', count: 17 },
      ],
    },
    {
      label: 'Score',
      value: 'score',
      type: 'slider',
      min: 1,
      max: 10,
    },
  ]

  // 定义列配置
  const columns: ColumnDef<Question>[] = [
    // 选择列 - 固定在左侧
    {
      id: "select",
      header: ({ table }) => (
        <div className="flex items-center justify-center">
          <input
            type="checkbox"
            checked={table.getIsAllPageRowsSelected()}
            onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
            className="rounded border border-input"
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center justify-center">
          <input
            type="checkbox"
            checked={row.getIsSelected()}
            onChange={(e) => row.toggleSelected(e.target.checked)}
            className="rounded border border-input"
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
      enableResizing: false,
      size: 50,
      minSize: 50,
      maxSize: 50,
    },

    // ID 列
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="ID" />
      ),
      cell: ({ getValue }) => (
        <div className="font-mono text-xs">
          {getValue() as number}
        </div>
      ),
      size: 60,
    },

    // 题目标题列
    {
      accessorKey: "title",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Title" />
      ),
      cell: ({ getValue }) => (
        <div className="font-normal max-w-[250px] truncate text-xs" title={getValue() as string}>
          {getValue() as string}
        </div>
      ),
      size: 250,
      minSize: 150,
      maxSize: 400,
    },

    // 题目内容列
    {
      accessorKey: "content",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Content" />
      ),
      cell: ({ getValue }) => (
        <div className="text-xs text-muted-foreground max-w-[350px] truncate" title={getValue() as string || ''}>
          {getValue() as string || '-'}
        </div>
      ),
      size: 350,
      minSize: 200,
      maxSize: 500,
    },

    // 类型列
    {
      accessorKey: "type",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Type" />
      ),
      cell: ({ getValue }) => {
        const type = getValue() as string
        return (
          <Badge variant={type === 'single' ? 'default' : 'secondary'} className="text-xs">
            {type === 'single' ? '单选题' : '多选题'}
          </Badge>
        )
      },
      size: 100,
    },

    // 分类列
    {
      accessorKey: "category",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Category" />
      ),
      cell: ({ getValue }) => (
        <Badge variant="outline" className="text-xs">
          {getValue() as string}
        </Badge>
      ),
      size: 100,
    },

    // 难度等级列
    {
      accessorKey: "difficulty",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Difficulty" />
      ),
      cell: ({ getValue }) => {
        const difficulty = getValue() as number
        const getDifficultyConfig = (level: number) => {
          switch (level) {
            case 1: return { label: "Easy", variant: "default" as const }
            case 2: return { label: "Medium", variant: "secondary" as const }
            case 3: return { label: "Normal", variant: "outline" as const }
            case 4: return { label: "Hard", variant: "destructive" as const }
            case 5: return { label: "Expert", variant: "destructive" as const }
            default: return { label: "Unknown", variant: "secondary" as const }
          }
        }

        const config = getDifficultyConfig(difficulty)

        return (
          <Badge variant={config.variant} className="text-xs">
            {config.label}
          </Badge>
        )
      },
      size: 100,
    },

    // 难度星级列
    {
      accessorKey: "difficulty",
      id: "difficulty_stars",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Stars" />
      ),
      cell: ({ getValue }) => {
        const difficulty = getValue() as number
        return (
          <div className="flex">
            {Array.from({ length: 5 }).map((_, i) => (
              <Star
                key={i}
                className={cn(
                  "h-3 w-3",
                  i < difficulty ? "text-yellow-400 fill-current" : "text-gray-300"
                )}
              />
            ))}
          </div>
        )
      },
      size: 80,
    },

    // 分值列
    {
      accessorKey: "score",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Score" />
      ),
      cell: ({ getValue }) => (
        <div className="text-center text-xs">
          {getValue() as number}
        </div>
      ),
      size: 70,
    },

    // 选项数量列
    {
      accessorKey: "options",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Options" />
      ),
      cell: ({ getValue }) => {
        const options = getValue() as any[]
        return (
          <div className="text-center text-xs text-muted-foreground">
            {options?.length || 0}
          </div>
        )
      },
      size: 80,
    },

    // 状态列
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ getValue }) => {
        const status = getValue() as number
        return (
          <Badge variant={status === 1 ? 'default' : 'secondary'} className="text-xs">
            {status === 1 ? 'Active' : 'Inactive'}
          </Badge>
        )
      },
      size: 80,
    },

    // 创建时间列
    {
      accessorKey: "created_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created" />
      ),
      cell: ({ getValue }) => {
        const date = getValue() as string
        return (
          <div className="text-xs text-muted-foreground font-light">
            {date ? new Date(date).toLocaleDateString() : '-'}
          </div>
        )
      },
      size: 100,
    },

    // 更新时间列
    {
      accessorKey: "updated_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Updated" />
      ),
      cell: ({ getValue }) => {
        const date = getValue() as string
        return (
          <div className="text-xs text-muted-foreground font-light">
            {date ? new Date(date).toLocaleDateString() : '-'}
          </div>
        )
      },
      size: 100,
    },

    // 操作列 - 固定在右侧
    {
      id: "actions",
      header: () => (
        <div className="text-center">Actions</div>
      ),
      cell: ({ row }) => {
        const question = row.original

        return (
          <div className="flex items-center justify-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {onView && (
                  <DropdownMenuItem onClick={() => onView(question)}>
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </DropdownMenuItem>
                )}
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(question)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                )}
                {onCopy && (
                  <DropdownMenuItem onClick={() => onCopy(question)}>
                    <Copy className="mr-2 h-4 w-4" />
                    Copy
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <DropdownMenuItem
                    onClick={() => onDelete(question)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )
      },
      enableSorting: false,
      enableHiding: false,
      enableResizing: false,
      size: 80,
      minSize: 80,
      maxSize: 80,
    },
  ]

  // 创建表格实例
  const table = useReactTable({
    data: flatData,
    columns,
    state: {
      rowSelection,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getRowId: (row) => String(row.id),
  })

  // 滚动处理 - 无限加载
  const tableContainerRef = React.useRef<HTMLDivElement>(null)

  const handleScroll = React.useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
      const onPageBottom = Math.ceil(scrollTop + clientHeight) >= scrollHeight - 100

      if (onPageBottom && !isFetching && hasNextPage) {
        fetchNextPage()
      }
    },
    [fetchNextPage, isFetching, hasNextPage]
  )

  return (
    <div className="flex h-full bg-background">
      {/* 左侧筛选栏 - 固定 */}
      <div className="w-64 border-r bg-background text-sm">
        <div className="p-3 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm">Filters</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {/* 重置筛选 */}}
              className="h-7 px-2 text-xs"
            >
              Reset
            </Button>
          </div>
        </div>

        <ScrollArea className="h-[calc(100%-80px)]">
          <div className="p-3">
            <div className="space-y-3">
              {filterFields.map((field) => (
                <div key={String(field.value)} className="space-y-2">
                  <h4 className="font-medium text-xs text-muted-foreground uppercase tracking-wide">{field.label}</h4>
                  <div className="space-y-1">
                    {field.options?.map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={option.value}
                          className="rounded border border-input w-3 h-3"
                        />
                        <label htmlFor={option.value} className="text-xs cursor-pointer">
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* 右侧主内容区 */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* 工具栏 - 固定 */}
        <div className="flex items-center justify-between p-3 border-b bg-background">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search questions..."
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="pl-7 w-56 h-8 text-xs"
              />
              {globalFilter && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 h-5 w-5 -translate-y-1/2 p-0"
                  onClick={() => setGlobalFilter("")}
                >
                  <X className="h-2.5 w-2.5" />
                </Button>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <div className="text-xs text-muted-foreground">
              {flatData.length} of {totalRows} rows
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              className="h-7 px-2"
              disabled={isLoading}
            >
              <RefreshCw className={`h-3.5 w-3.5 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* 表格容器 - 只有这部分可以滚动 */}
        <div className="flex-1 relative overflow-hidden">
          <div
            ref={tableContainerRef}
            className="absolute inset-0 overflow-auto"
            onScroll={handleScroll}
          >
            <Table className="relative text-xs">
              {/* 表头 - 粘性定位 */}
              <TableHeader className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b shadow-sm">
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="border-b hover:bg-transparent">
                    {headerGroup.headers.map((header) => {
                      const isSelectColumn = header.id === 'select'
                      const isActionsColumn = header.id === 'actions'

                      return (
                        <TableHead
                          key={header.id}
                          className={cn(
                            "relative border-r last:border-r-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 h-9 px-2 text-xs font-normal text-muted-foreground",
                            // 固定列样式
                            isSelectColumn && "sticky left-0 z-[51] shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]",
                            isActionsColumn && "sticky right-0 z-[51] shadow-[-2px_0_5px_-2px_rgba(0,0,0,0.1)]"
                          )}
                          style={{
                            width: isSelectColumn ? '50px' : isActionsColumn ? '80px' : 'auto',
                          }}
                        >
                          {header.isPlaceholder ? null : (
                            flexRender(header.column.columnDef.header, header.getContext())
                          )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>

              {/* 表体 */}
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="border-r-0 [&>:not(:last-child)]:border-r cursor-pointer hover:bg-muted/50 h-10"
                      onClick={() => row.toggleSelected()}
                    >
                      {row.getVisibleCells().map((cell) => {
                        const isSelectColumn = cell.column.id === 'select'
                        const isActionsColumn = cell.column.id === 'actions'

                        return (
                          <TableCell
                            key={cell.id}
                            className={cn(
                              "border-r last:border-r-0 bg-background px-2 py-1 text-xs",
                              // 固定列样式
                              isSelectColumn && "sticky left-0 z-20 shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)] bg-background",
                              isActionsColumn && "sticky right-0 z-20 shadow-[-2px_0_5px_-2px_rgba(0,0,0,0.1)] bg-background"
                            )}
                            style={{
                              width: isSelectColumn ? '50px' : isActionsColumn ? '80px' : 'auto',
                            }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        )
                      })}
                    </TableRow>
                  ))
                ) : isLoading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span className="text-muted-foreground">Loading...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      <div className="text-muted-foreground">No results.</div>
                    </TableCell>
                  </TableRow>
                )}

                {/* 加载更多指示器 */}
                {isFetching && flatData.length > 0 && (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-12 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-3 w-3 animate-spin" />
                        <span className="text-xs text-muted-foreground">Loading more...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {/* 没有更多数据 */}
                {!hasNextPage && !isFetching && flatData.length > 0 && (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-12 text-center">
                      <div className="text-xs text-muted-foreground">
                        No more data to load ({flatData.length} of {totalRows} rows)
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  )
}

// 导出类型
export type { OpenStatusInfiniteQuestionsTableProps }
