import { createFileRoute } from '@tanstack/react-router'
import { useQuery } from '@tanstack/react-query'
import { MainLayout } from '@/components/layout/main-layout'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { userApi, questionApi, answerApi } from '@/lib/api'
import { Users, FileQuestion, BarChart3, TrendingUp } from 'lucide-react'
import { formatNumber } from '@/lib/utils'

export const Route = createFileRoute('/dashboard')({
  component: DashboardPage,
})

function DashboardPage() {
  const { data: userStats } = useQuery({
    queryKey: ['user-stats'],
    queryFn: userApi.getUserStats,
  })

  const { data: questionStats } = useQuery({
    queryKey: ['question-stats'],
    queryFn: questionApi.getQuestionStats,
  })

  const { data: answerStats } = useQuery({
    queryKey: ['answer-stats'],
    queryFn: answerApi.getAnswerStats,
  })

  const stats = [
    {
      title: '总用户数',
      value: userStats?.data?.total_users || 0,
      description: '系统注册用户总数',
      icon: Users,
      color: 'text-blue-600',
    },
    {
      title: '题目总数',
      value: questionStats?.data?.total_questions || 0,
      description: '题库中的题目数量',
      icon: FileQuestion,
      color: 'text-green-600',
    },
    {
      title: '答题记录',
      value: answerStats?.data?.total_answers || 0,
      description: '用户答题总次数',
      icon: BarChart3,
      color: 'text-purple-600',
    },
    {
      title: '活跃用户',
      value: userStats?.data?.active_users || 0,
      description: '最近30天活跃用户',
      icon: TrendingUp,
      color: 'text-orange-600',
    },
  ]

  return (
    <MainLayout title="仪表盘" subtitle="欢迎回来，这里是系统概览">
      <div className="space-y-4 p-4">
        {/* Stats Cards */}
        <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => {
            const Icon = stat.icon
            return (
              <Card key={stat.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-xs font-medium text-muted-foreground">
                    {stat.title}
                  </CardTitle>
                  <Icon className={`h-3.5 w-3.5 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-lg font-medium">
                    {formatNumber(stat.value)}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {stat.description}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Charts and Additional Info */}
        <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-7">
          <Card className="col-span-4">
            <CardHeader>
              <CardTitle>用户角色分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {userStats?.data?.role_stats?.map((role: any) => (
                  <div key={role.role} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-primary" />
                      <span className="text-xs">
                        {role.role === 'super_admin' ? '超级管理员' :
                         role.role === 'admin' ? '管理员' : '普通用户'}
                      </span>
                    </div>
                    <span className="text-xs font-medium">
                      {formatNumber(role.count)}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-3">
            <CardHeader>
              <CardTitle>系统状态</CardTitle>
              <CardDescription className="text-xs">
                系统运行状态概览
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-xs">数据库连接</span>
                  <div className="flex items-center space-x-1.5">
                    <div className="w-1.5 h-1.5 rounded-full bg-green-500" />
                    <span className="text-xs text-green-600">正常</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs">API服务</span>
                  <div className="flex items-center space-x-1.5">
                    <div className="w-1.5 h-1.5 rounded-full bg-green-500" />
                    <span className="text-xs text-green-600">运行中</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs">缓存服务</span>
                  <div className="flex items-center space-x-1.5">
                    <div className="w-1.5 h-1.5 rounded-full bg-yellow-500" />
                    <span className="text-xs text-yellow-600">未配置</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
