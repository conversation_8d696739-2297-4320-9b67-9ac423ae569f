import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Database, 
  Trash2, 
  RefreshCw, 
  Plus, 
  AlertTriangle,
  CheckCircle,
  Loader2,
  BarChart3,
  TrendingUp,
  Users,
  BookOpen
} from 'lucide-react'
import { generateFakeData, clearAllData, resetData } from '@/utils/generate-fake-data'
import { toast } from 'sonner'
import { createFileRoute } from '@tanstack/react-router'

export const Route = createFileRoute('/data-generator')({
  component: DataGeneratorPage,
})

export default function DataGeneratorPage() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [isClearing, setIsClearing] = useState(false)
  const [isResetting, setIsResetting] = useState(false)
  const [count, setCount] = useState(50)
  const [progress, setProgress] = useState(0)
  const [stats, setStats] = useState<{
    generated: number
    failed: number
    total: number
  } | null>(null)

  const handleGenerate = async () => {
    if (count <= 0 || count > 1000) {
      toast.error('请输入有效的数量 (1-1000)')
      return
    }

    setIsGenerating(true)
    setProgress(0)
    setStats(null)

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + Math.random() * 10, 90))
      }, 200)

      await generateFakeData(count)
      
      clearInterval(progressInterval)
      setProgress(100)
      
      setStats({
        generated: count,
        failed: 0,
        total: count
      })

      toast.success(`成功生成 ${count} 条假数据！`)
    } catch (error) {
      console.error('生成数据失败:', error)
      toast.error('生成数据失败，请检查网络连接和后端服务')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleClear = async () => {
    if (!confirm('确定要清空所有题目数据吗？此操作不可恢复！')) {
      return
    }

    setIsClearing(true)
    try {
      await clearAllData()
      toast.success('成功清空所有数据！')
    } catch (error) {
      console.error('清空数据失败:', error)
      toast.error('清空数据失败，请检查网络连接和后端服务')
    } finally {
      setIsClearing(false)
    }
  }

  const handleReset = async () => {
    if (!confirm(`确定要重置数据吗？这将清空所有现有数据并生成 ${count} 条新数据！`)) {
      return
    }

    setIsResetting(true)
    setProgress(0)
    setStats(null)

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + Math.random() * 8, 90))
      }, 300)

      await resetData(count)
      
      clearInterval(progressInterval)
      setProgress(100)
      
      setStats({
        generated: count,
        failed: 0,
        total: count
      })

      toast.success(`成功重置并生成 ${count} 条数据！`)
    } catch (error) {
      console.error('重置数据失败:', error)
      toast.error('重置数据失败，请检查网络连接和后端服务')
    } finally {
      setIsResetting(false)
    }
  }

  const isLoading = isGenerating || isClearing || isResetting

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">数据生成器</h1>
          <p className="text-muted-foreground">
            生成测试数据用于开发和测试，包含各种类型的题目和选项
          </p>
        </div>

        {/* 功能卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">生成数据</CardTitle>
              <Plus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">+{count}</div>
              <p className="text-xs text-muted-foreground">
                将生成的题目数量
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">数据类型</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">10</div>
              <p className="text-xs text-muted-foreground">
                支持的题目分类
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">难度等级</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">5</div>
              <p className="text-xs text-muted-foreground">
                从简单到困难
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 主要操作区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 生成数据 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>生成假数据</span>
              </CardTitle>
              <CardDescription>
                生成指定数量的测试题目数据，包含多种分类和难度等级
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="count">生成数量</Label>
                <Input
                  id="count"
                  type="number"
                  min="1"
                  max="1000"
                  value={count}
                  onChange={(e) => setCount(parseInt(e.target.value) || 50)}
                  placeholder="输入要生成的题目数量"
                  disabled={isLoading}
                />
                <p className="text-sm text-muted-foreground">
                  建议生成 10-100 条数据用于测试
                </p>
              </div>

              {(isGenerating || isResetting) && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>生成进度</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                  <Progress value={progress} className="w-full" />
                </div>
              )}

              <div className="flex space-x-2">
                <Button 
                  onClick={handleGenerate} 
                  disabled={isLoading}
                  className="flex-1"
                >
                  {isGenerating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  生成数据
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleReset} 
                  disabled={isLoading}
                  className="flex-1"
                >
                  {isResetting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  重置数据
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 数据管理 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Trash2 className="h-5 w-5" />
                <span>数据管理</span>
              </CardTitle>
              <CardDescription>
                清空现有数据或查看数据统计信息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  清空操作将删除所有题目数据，请谨慎操作！
                </AlertDescription>
              </Alert>

              <Button 
                variant="destructive" 
                onClick={handleClear} 
                disabled={isLoading}
                className="w-full"
              >
                {isClearing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                清空所有数据
              </Button>

              {stats && (
                <div className="space-y-3">
                  <Separator />
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium flex items-center">
                      <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                      操作完成
                    </h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex justify-between">
                        <span>成功:</span>
                        <Badge variant="default">{stats.generated}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>失败:</span>
                        <Badge variant={stats.failed > 0 ? "destructive" : "secondary"}>
                          {stats.failed}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 数据说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>生成数据说明</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">题目类型</h4>
                <div className="space-y-1">
                  <Badge variant="outline">单选题</Badge>
                  <Badge variant="outline">多选题</Badge>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">题目分类</h4>
                <div className="flex flex-wrap gap-1">
                  {['数学', '语文', '英语', '物理', '化学'].map(category => (
                    <Badge key={category} variant="secondary" className="text-xs">
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">难度等级</h4>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">1-5 星难度</div>
                  <div className="text-sm text-muted-foreground">随机分配</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">其他属性</h4>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">随机分值 (1-10)</div>
                  <div className="text-sm text-muted-foreground">随机状态</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
