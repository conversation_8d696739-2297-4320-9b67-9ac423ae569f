import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { MainLayout } from '@/components/layout/main-layout'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { QuestionForm } from '@/components/forms/question-form'
import { QuestionsTable } from '@/components/tables/questions-table'
import { questionApi, Question, CreateQuestionRequest } from '@/lib/api'
import { toast } from 'sonner'

export const Route = createFileRoute('/questions')({
  component: QuestionsPage,
})

function QuestionsPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null)

  const queryClient = useQueryClient()



  const createMutation = useMutation({
    mutationFn: questionApi.createQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      setIsCreateDialogOpen(false)
      toast.success('题目创建成功')
    },
    onError: () => {
      toast.error('创建失败')
    },
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: CreateQuestionRequest }) =>
      questionApi.updateQuestion(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      setEditingQuestion(null)
      toast.success('题目更新成功')
    },
    onError: () => {
      toast.error('更新失败')
    },
  })

  const deleteMutation = useMutation({
    mutationFn: questionApi.deleteQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast.success('题目删除成功')
    },
    onError: () => {
      toast.error('删除失败')
    },
  })

  const handleCreateQuestion = async (data: CreateQuestionRequest) => {
    return new Promise<void>((resolve, reject) => {
      createMutation.mutate(data, {
        onSuccess: () => resolve(),
        onError: (error) => reject(error)
      })
    })
  }

  const handleUpdateQuestion = async (data: CreateQuestionRequest) => {
    if (editingQuestion) {
      return new Promise<void>((resolve, reject) => {
        updateMutation.mutate({ id: editingQuestion.id, data }, {
          onSuccess: () => resolve(),
          onError: (error) => reject(error)
        })
      })
    }
    return Promise.resolve()
  }

  const handleDeleteQuestion = (question: Question) => {
    if (confirm('确定要删除这个题目吗？')) {
      deleteMutation.mutate(question.id)
    }
  }





  return (
    <MainLayout title="试题管理" subtitle="管理系统试题库和题目内容">
      <div className="h-full">
        <QuestionsTable
          queryKey={['questions']}
          queryFn={async ({ page, pageSize }) => {
            const response = await questionApi.getQuestions({
              page,
              page_size: pageSize
            })
            return {
              data: response.data?.list || [],
              total: response.data?.total || 0,
              hasNextPage: page * pageSize < (response.data?.total || 0)
            }
          }}
          onAdd={() => setIsCreateDialogOpen(true)}
          onEdit={setEditingQuestion}
          onDelete={handleDeleteQuestion}
          onView={(question: Question) => {
            // TODO: 实现查看详情
            console.log('查看题目详情:', question)
            toast.info('查看详情功能开发中...')
          }}
          onCopy={(question: Question) => {
            // TODO: 实现复制题目
            console.log('复制题目:', question)
            toast.success('题目复制功能开发中...')
          }}
          enableRowSelection={false}
        />

        {/* 创建题目对话框 */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
            <DialogHeader className="flex-shrink-0">
              <DialogTitle>创建题目</DialogTitle>
              <DialogDescription>
                填写题目信息并设置选项
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto">
              <QuestionForm
                onSubmit={handleCreateQuestion}
                onCancel={() => setIsCreateDialogOpen(false)}
                isLoading={createMutation.isPending}
                hideButtons={true}
              />
            </div>
            <DialogFooter className="flex-shrink-0 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
                disabled={createMutation.isPending}
              >
                取消
              </Button>
              <Button
                type="submit"
                form="question-form"
                disabled={createMutation.isPending}
              >
                {createMutation.isPending ? '创建中...' : '创建题目'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 编辑题目对话框 */}
        <Dialog open={!!editingQuestion} onOpenChange={() => setEditingQuestion(null)}>
          <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
            <DialogHeader className="flex-shrink-0">
              <DialogTitle>编辑题目</DialogTitle>
              <DialogDescription>
                修改题目信息和选项设置
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto">
              {editingQuestion && (
                <QuestionForm
                  question={editingQuestion}
                  onSubmit={handleUpdateQuestion}
                  onCancel={() => setEditingQuestion(null)}
                  isLoading={updateMutation.isPending}
                  hideButtons={true}
                />
              )}
            </div>
            <DialogFooter className="flex-shrink-0 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditingQuestion(null)}
                disabled={updateMutation.isPending}
              >
                取消
              </Button>
              <Button
                type="submit"
                form="question-form"
                disabled={updateMutation.isPending}
              >
                {updateMutation.isPending ? '更新中...' : '更新题目'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
