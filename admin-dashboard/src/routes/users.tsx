import { createFileRoute } from '@tanstack/react-router'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { MainLayout } from '@/components/layout/main-layout'
import { UsersTable } from '@/components/tables/users-table'
import { userApi, User } from '@/lib/api'
import { toast } from 'sonner'

export const Route = createFileRoute('/users')({
  component: UsersPage,
})

function UsersPage() {
  const queryClient = useQueryClient()

  // 查询函数
  const queryFn = async ({ page, pageSize }: { page: number; pageSize: number }) => {
    const response = await userApi.getUsers({
      page,
      page_size: pageSize,
    })
    return {
      data: response.data?.list || [],
      total: response.data?.total || 0,
      totalPages: response.data?.total_pages || 0,
    }
  }

  // Mutations
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: number; status: number }) =>
      userApi.updateUserStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('用户状态更新成功')
    },
    onError: () => {
      toast.error('更新失败')
    },
  })

  const promoteToAdminMutation = useMutation({
    mutationFn: (id: number) => userApi.promoteToAdmin(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('用户已提升为管理员')
    },
    onError: () => {
      toast.error('操作失败')
    },
  })

  const promoteToSuperAdminMutation = useMutation({
    mutationFn: (id: number) => userApi.promoteToSuperAdmin(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('用户已提升为超级管理员')
    },
    onError: () => {
      toast.error('操作失败')
    },
  })

  const demoteToUserMutation = useMutation({
    mutationFn: (id: number) => userApi.demoteToUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('用户已降级为普通用户')
    },
    onError: () => {
      toast.error('操作失败')
    },
  })

  // 处理函数
  const handleUpdateStatus = (user: User, status: number) => {
    updateStatusMutation.mutate({ id: user.id, status })
  }

  const handlePromoteToAdmin = (user: User) => {
    promoteToAdminMutation.mutate(user.id)
  }

  const handlePromoteToSuperAdmin = (user: User) => {
    promoteToSuperAdminMutation.mutate(user.id)
  }

  const handleDemoteToUser = (user: User) => {
    demoteToUserMutation.mutate(user.id)
  }

  const handleEdit = (user: User) => {
    // TODO: 实现编辑用户功能
    console.log('Edit user:', user)
  }

  const handleView = (user: User) => {
    // TODO: 实现查看用户详情功能
    console.log('View user:', user)
  }



  return (
    <MainLayout title="用户管理" subtitle="管理系统用户和权限">
      <div className="h-full">
        <UsersTable
          queryFn={queryFn}
          queryKey={['users']}
          onEdit={handleEdit}
          onView={handleView}
          onUpdateStatus={handleUpdateStatus}
          onPromoteToAdmin={handlePromoteToAdmin}
          onPromoteToSuperAdmin={handlePromoteToSuperAdmin}
          onDemoteToUser={handleDemoteToUser}
          enableRowSelection={false}
        />
      </div>
    </MainLayout>
  )
}
