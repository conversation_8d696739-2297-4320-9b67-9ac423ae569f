/**
 * 生成假数据工具函数
 * 用于向数据库插入测试题目数据
 */

import { questionApi, type CreateQuestionRequest } from '@/lib/api'

// 题目类型
const QUESTION_TYPES = ['single', 'multiple'] as const

// 题目分类
const CATEGORIES = ['数学', '语文', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '计算机']

// 难度等级
const DIFFICULTIES = [1, 2, 3, 4, 5]

// 状态
const STATUSES = [0, 1]

// 生成随机题目标题
function generateQuestionTitle(category: string, index: number): string {
  const titleTemplates: Record<string, string[]> = {
    '数学': [
      '求解二次方程',
      '计算三角函数值',
      '求导数',
      '计算积分',
      '解不等式',
      '求极限',
      '矩阵运算',
      '概率计算'
    ],
    '语文': [
      '古诗词理解',
      '文言文翻译',
      '现代文阅读',
      '作文写作技巧',
      '修辞手法分析',
      '字词辨析',
      '语法分析',
      '文学常识'
    ],
    '英语': [
      'Grammar Analysis',
      'Reading Comprehension',
      'Vocabulary Usage',
      'Sentence Translation',
      'Listening Practice',
      'Writing Skills',
      'Pronunciation',
      'Idiom Understanding'
    ],
    '物理': [
      '力学分析',
      '电磁学原理',
      '光学现象',
      '热力学定律',
      '量子物理',
      '相对论',
      '波动理论',
      '原子结构'
    ],
    '化学': [
      '化学反应方程式',
      '有机化学结构',
      '无机化学性质',
      '化学平衡',
      '电化学原理',
      '化学键理论',
      '溶液配制',
      '化学实验'
    ]
  }

  const templates = titleTemplates[category] || ['基础知识', '概念理解', '应用分析', '综合题目']
  const template = templates[index % templates.length]
  return `${template} - 第${index + 1}题`
}

// 生成随机题目内容
function generateQuestionContent(category: string, title: string): string {
  const contentTemplates: Record<string, string> = {
    '数学': `请根据给定条件求解 ${title}。要求写出详细的解题步骤，并验证答案的正确性。`,
    '语文': `阅读以下材料，回答关于 ${title} 的相关问题。请结合文本内容进行分析。`,
    '英语': `Based on the given context, answer the question about ${title}. Provide detailed explanations.`,
    '物理': `根据物理定律和原理，分析 ${title} 的相关问题。请画出必要的图示并说明。`,
    '化学': `根据化学原理，解释 ${title} 的相关现象。写出相关的化学方程式。`
  }

  return contentTemplates[category] || `请回答关于 ${title} 的相关问题，并提供详细的解释和分析。`
}

// 生成选项
function generateOptions(type: string, category: string) {
  const optionCount = type === 'single' ? 4 : Math.floor(Math.random() * 3) + 4 // 单选4个，多选4-6个
  const options = []

  for (let i = 0; i < optionCount; i++) {
    const label = String.fromCharCode(65 + i) // A, B, C, D...
    let content = ''

    // 根据分类生成不同类型的选项内容
    switch (category) {
      case '数学':
        content = `选项${label}: ${Math.floor(Math.random() * 100)} + ${Math.floor(Math.random() * 100)} = ${Math.floor(Math.random() * 200)}`
        break
      case '英语':
        const englishOptions = ['apple', 'banana', 'orange', 'grape', 'watermelon', 'strawberry']
        content = `Option ${label}: ${englishOptions[Math.floor(Math.random() * englishOptions.length)]}`
        break
      case '物理':
        content = `选项${label}: F = ma, 其中 a = ${Math.floor(Math.random() * 10)} m/s²`
        break
      case '化学':
        const elements = ['H₂O', 'CO₂', 'NaCl', 'CaCO₃', 'H₂SO₄']
        content = `选项${label}: ${elements[Math.floor(Math.random() * elements.length)]}`
        break
      default:
        content = `选项${label}: 这是选项${label}的内容描述`
    }

    // 随机设置正确答案（单选题1个，多选题1-2个）
    let isCorrect = false
    if (type === 'single') {
      isCorrect = i === 0 // 第一个选项为正确答案
    } else {
      isCorrect = Math.random() < 0.3 && options.filter(opt => opt.is_correct).length < 2
    }

    options.push({
      label: label,
      content: content,
      is_correct: isCorrect,
      sort: i + 1
    })
  }

  // 确保至少有一个正确答案
  if (!options.some(opt => opt.is_correct)) {
    options[0].is_correct = true
  }

  return options
}

// 生成单个题目数据
function generateSingleQuestionData(index: number): CreateQuestionRequest {
  // 随机选择分类和类型
  const category = CATEGORIES[Math.floor(Math.random() * CATEGORIES.length)]
  const type = QUESTION_TYPES[Math.floor(Math.random() * QUESTION_TYPES.length)]
  const difficulty = DIFFICULTIES[Math.floor(Math.random() * DIFFICULTIES.length)]
  const status = STATUSES[Math.floor(Math.random() * STATUSES.length)]
  const score = Math.floor(Math.random() * 10) + 1

  // 生成题目数据
  const title = generateQuestionTitle(category, index)
  const content = generateQuestionContent(category, title)
  const options = generateOptions(type, category)

  return {
    title: title,
    content: content,
    type: type,
    category: category,
    difficulty: difficulty,
    score: score,
    status: status,
    sort: index + 1,
    created_by: 1,
    options: options
  }
}

// 生成指定数量的假数据
export async function generateFakeData(count: number = 50): Promise<void> {
  console.log(`开始生成 ${count} 条假数据...`)

  const createdQuestions = []

  for (let i = 0; i < count; i++) {
    try {
      const questionData = generateSingleQuestionData(i)

      console.log(`正在创建第 ${i + 1} 个题目: ${questionData.title}`)
      console.log('题目数据:', questionData) // 调试日志

      const response = await questionApi.create(questionData)
      console.log('API 响应:', response) // 调试日志

      createdQuestions.push(response.data)

      // 添加小延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 100))

    } catch (error) {
      console.error(`创建第 ${i + 1} 个题目失败:`, error)
      console.error('错误详情:', error.response?.data || error.message)
      continue
    }
  }

  console.log(`\n✅ 成功生成 ${createdQuestions.length} 条假数据！`)
  
  // 统计数据
  const stats = {
    types: {} as Record<string, number>,
    categories: {} as Record<string, number>,
    difficulties: {} as Record<string, number>,
    statuses: {} as Record<string, number>
  }

  createdQuestions.forEach(question => {
    stats.types[question.type] = (stats.types[question.type] || 0) + 1
    stats.categories[question.category] = (stats.categories[question.category] || 0) + 1
    stats.difficulties[question.difficulty] = (stats.difficulties[question.difficulty] || 0) + 1
    stats.statuses[question.status] = (stats.statuses[question.status] || 0) + 1
  })

  console.log('\n生成的数据统计:')
  console.log('题目类型分布:', stats.types)
  console.log('分类分布:', stats.categories)
  console.log('难度分布:', stats.difficulties)
  console.log('状态分布:', stats.statuses)
}

// 清空所有题目数据
export async function clearAllData(): Promise<void> {
  try {
    console.log('正在清空所有题目数据...')
    
    // 获取所有题目
    const response = await questionApi.list({ page: 1, limit: 1000 })
    const questions = response.data.list || []
    
    // 删除所有题目
    for (const question of questions) {
      try {
        await questionApi.delete(question.id)
        console.log(`已删除题目: ${question.title}`)
      } catch (error) {
        console.error(`删除题目失败 (ID: ${question.id}):`, error)
      }
    }
    
    console.log(`✅ 成功清空 ${questions.length} 条数据！`)
  } catch (error) {
    console.error('清空数据失败:', error)
  }
}

// 重置数据（清空后重新生成）
export async function resetData(count: number = 50): Promise<void> {
  await clearAllData()
  await generateFakeData(count)
}

// 导出生成单个题目数据的函数，供其他地方使用
export { generateSingleQuestionData }
