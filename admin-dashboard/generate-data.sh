#!/bin/bash

# 生成假数据脚本
# 使用方法: ./generate-data.sh [数量]

API_URL="http://localhost:3000/api/v1/questions"
COUNT=${1:-10}  # 默认生成10条数据

# 认证 token - 你需要替换为真实的 token
AUTH_TOKEN=${2:-"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJvcGVuX2lkIjoic3VwZXJfYWRtaW5fZGVmYXVsdCIsImV4cCI6MTc1NDA0MDQ3NCwibmJmIjoxNzUzOTU0MDc0LCJpYXQiOjE3NTM5NTQwNzR9.RCRnyML9ztDdtrMJzHxxEDxhIW0aLCSRhzTYLLzL2Po"}

echo "开始生成 $COUNT 条假数据..."
echo "API URL: $API_URL"
echo "Auth Token: ${AUTH_TOKEN:0:20}..." # 只显示前20个字符

# 题目数据数组
declare -a QUESTIONS=(
  '{
    "title": "计算二次方程的解",
    "content": "求解方程 x² - 5x + 6 = 0 的所有实数解",
    "type": "single",
    "category": "数学",
    "difficulty": 2,
    "score": 5,
    "status": 1,
    "sort": 1,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "x = 2, x = 3", "is_correct": true, "sort": 1},
      {"label": "B", "content": "x = 1, x = 6", "is_correct": false, "sort": 2},
      {"label": "C", "content": "x = -2, x = -3", "is_correct": false, "sort": 3},
      {"label": "D", "content": "无实数解", "is_correct": false, "sort": 4}
    ]
  }'
  
  '{
    "title": "英语语法选择",
    "content": "Choose the correct form: She _____ to school every day.",
    "type": "single",
    "category": "英语",
    "difficulty": 1,
    "score": 3,
    "status": 1,
    "sort": 2,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "go", "is_correct": false, "sort": 1},
      {"label": "B", "content": "goes", "is_correct": true, "sort": 2},
      {"label": "C", "content": "going", "is_correct": false, "sort": 3},
      {"label": "D", "content": "gone", "is_correct": false, "sort": 4}
    ]
  }'
  
  '{
    "title": "物理力学分析",
    "content": "一个质量为2kg的物体受到10N的力，求加速度",
    "type": "single",
    "category": "物理",
    "difficulty": 3,
    "score": 8,
    "status": 1,
    "sort": 3,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "5 m/s²", "is_correct": true, "sort": 1},
      {"label": "B", "content": "10 m/s²", "is_correct": false, "sort": 2},
      {"label": "C", "content": "2 m/s²", "is_correct": false, "sort": 3},
      {"label": "D", "content": "20 m/s²", "is_correct": false, "sort": 4}
    ]
  }'
  
  '{
    "title": "化学反应方程式",
    "content": "下列哪些是氧化还原反应？",
    "type": "multiple",
    "category": "化学",
    "difficulty": 4,
    "score": 10,
    "status": 1,
    "sort": 4,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "2H₂ + O₂ → 2H₂O", "is_correct": true, "sort": 1},
      {"label": "B", "content": "NaCl + AgNO₃ → AgCl + NaNO₃", "is_correct": false, "sort": 2},
      {"label": "C", "content": "Zn + CuSO₄ → ZnSO₄ + Cu", "is_correct": true, "sort": 3},
      {"label": "D", "content": "HCl + NaOH → NaCl + H₂O", "is_correct": false, "sort": 4}
    ]
  }'
  
  '{
    "title": "古诗词理解",
    "content": "李白《静夜思》表达了什么情感？",
    "type": "single",
    "category": "语文",
    "difficulty": 2,
    "score": 6,
    "status": 1,
    "sort": 5,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "思乡之情", "is_correct": true, "sort": 1},
      {"label": "B", "content": "赞美月亮", "is_correct": false, "sort": 2},
      {"label": "C", "content": "恐惧夜晚", "is_correct": false, "sort": 3},
      {"label": "D", "content": "憧憬未来", "is_correct": false, "sort": 4}
    ]
  }'
)

# 生成数据
success_count=0
for ((i=1; i<=COUNT; i++)); do
  # 选择题目模板
  template_index=$((($i - 1) % ${#QUESTIONS[@]}))
  question_data=${QUESTIONS[$template_index]}
  
  # 修改题目标题和排序
  modified_data=$(echo "$question_data" | sed "s/\"title\": \"/\"title\": \"第${i}题 - /g" | sed "s/\"sort\": [0-9]*/\"sort\": $i/g")
  
  echo "正在创建第 $i 个题目..."
  
  # 发送 POST 请求
  response=$(curl -s -w "%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $AUTH_TOKEN" \
    -d "$modified_data" \
    "$API_URL")
  
  # 获取 HTTP 状态码
  http_code="${response: -3}"
  response_body="${response%???}"
  
  if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 201 ]; then
    echo "✅ 第 $i 个题目创建成功"
    ((success_count++))
  else
    echo "❌ 第 $i 个题目创建失败 (HTTP $http_code)"
    echo "响应: $response_body"
  fi
  
  # 添加延迟
  sleep 0.1
done

echo ""
echo "🎉 完成！成功创建 $success_count/$COUNT 个题目"
