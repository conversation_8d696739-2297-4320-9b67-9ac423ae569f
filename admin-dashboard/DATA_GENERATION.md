# 数据生成工具使用指南

本项目提供了多种方式来生成测试数据，用于开发和测试题目管理系统。

## 🚀 快速开始

### 方法一：使用界面生成（推荐）

1. 启动开发服务器：
```bash
npm run dev
```

2. 访问数据生成页面：
```
http://localhost:5173/data-generator
```

3. 在界面上设置生成数量并点击"生成数据"按钮

### 方法二：使用命令行快速生成

```bash
# 生成 20 条数据（默认）
npm run quick-generate

# 生成指定数量的数据
npm run quick-generate 50
```

### 方法三：使用完整的数据生成脚本

```bash
# 生成 50 条数据（默认）
npm run generate-data

# 清空所有数据
npm run clear-data

# 重置数据（清空后重新生成）
npm run reset-data
```

## 📊 生成的数据类型

### 题目分类
- 数学
- 语文  
- 英语
- 物理
- 化学
- 生物
- 历史
- 地理
- 政治
- 计算机

### 题目类型
- **单选题**: 4个选项，1个正确答案
- **多选题**: 4-6个选项，1-2个正确答案

### 难度等级
- 1星：简单
- 2星：容易
- 3星：中等
- 4星：困难
- 5星：极难

### 其他属性
- **分值**: 随机 1-10 分
- **状态**: 80% 启用，20% 禁用
- **创建时间**: 随机过去30天内
- **排序**: 按创建顺序

## 🛠️ 数据生成工具对比

| 工具 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| 界面生成 | 直观易用，实时反馈 | 需要启动前端 | 日常开发测试 |
| 快速生成 | 速度快，质量高 | 数据类型固定 | 快速原型验证 |
| 完整脚本 | 功能全面，可定制 | 配置复杂 | 大量数据生成 |

## 📝 生成数据示例

### 数学题示例
```json
{
  "title": "计算二次方程的解 - 第1题",
  "content": "求解方程 x² - 5x + 6 = 0 的所有实数解",
  "type": "single",
  "category": "数学",
  "difficulty": 2,
  "score": 5,
  "options": [
    {"label": "A", "content": "x = 2, x = 3", "is_correct": true},
    {"label": "B", "content": "x = 1, x = 6", "is_correct": false},
    {"label": "C", "content": "x = -2, x = -3", "is_correct": false},
    {"label": "D", "content": "无实数解", "is_correct": false}
  ]
}
```

### 英语题示例
```json
{
  "title": "英语语法选择 - 第2题",
  "content": "Choose the correct form: She _____ to school every day.",
  "type": "single",
  "category": "英语",
  "difficulty": 1,
  "score": 3,
  "options": [
    {"label": "A", "content": "go", "is_correct": false},
    {"label": "B", "content": "goes", "is_correct": true},
    {"label": "C", "content": "going", "is_correct": false},
    {"label": "D", "content": "gone", "is_correct": false}
  ]
}
```

## ⚙️ 自定义数据生成

### 修改题目模板

编辑 `scripts/quick-generate.js` 中的 `QUESTION_TEMPLATES` 数组：

```javascript
const QUESTION_TEMPLATES = [
  {
    title: '你的题目标题',
    content: '你的题目内容',
    type: 'single', // 或 'multiple'
    category: '你的分类',
    difficulty: 3,
    score: 5,
    status: 1,
    options: [
      { label: 'A', content: '选项A', is_correct: true, sort: 1 },
      // ... 更多选项
    ]
  }
]
```

### 修改生成逻辑

编辑 `src/utils/generate-fake-data.ts` 中的生成函数：

```typescript
// 修改分类列表
const CATEGORIES = ['你的分类1', '你的分类2', ...]

// 修改题目标题生成逻辑
function generateQuestionTitle(category: string, index: number): string {
  // 你的自定义逻辑
}
```

## 🔧 故障排除

### 常见问题

1. **生成失败**: 确保后端 API 服务正在运行
2. **网络错误**: 检查 API 基础 URL 配置
3. **权限错误**: 确保有创建题目的权限
4. **数据重复**: 使用"重置数据"功能清空后重新生成

### 调试模式

启用详细日志：
```bash
DEBUG=true npm run quick-generate
```

### API 配置

修改 API 基础 URL（如果后端端口不是 3000）：
```javascript
// scripts/quick-generate.js
const API_BASE_URL = 'http://localhost:你的端口/api'
```

## 📈 性能建议

1. **小批量生成**: 建议每次生成不超过 100 条数据
2. **网络延迟**: 脚本已内置延迟，避免请求过快
3. **内存使用**: 大量数据生成时注意内存使用情况
4. **数据库性能**: 定期清理测试数据，保持数据库性能

## 🎯 最佳实践

1. **开发环境**: 使用 10-50 条数据进行日常开发
2. **测试环境**: 使用 100-500 条数据进行功能测试
3. **压力测试**: 使用 1000+ 条数据进行性能测试
4. **数据清理**: 定期清理不需要的测试数据

## 📚 相关文档

- [API 文档](../backend/API.md)
- [数据库设计](../backend/DATABASE.md)
- [前端组件文档](./COMPONENTS.md)

---

如有问题，请查看项目 Issues 或联系开发团队。
