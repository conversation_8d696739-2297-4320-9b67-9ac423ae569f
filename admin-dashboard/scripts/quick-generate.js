#!/usr/bin/env node

/**
 * 快速生成假数据脚本
 * 使用方法: node scripts/quick-generate.js [数量]
 */

const axios = require('axios')

// API 基础配置
const API_BASE_URL = 'http://localhost:3000/api'

// 预定义的题目数据模板
const QUESTION_TEMPLATES = [
  {
    title: '计算二次方程的解',
    content: '求解方程 x² - 5x + 6 = 0 的所有实数解',
    type: 'single',
    category: '数学',
    difficulty: 2,
    score: 5,
    status: 1,
    options: [
      { label: 'A', content: 'x = 2, x = 3', is_correct: true, sort: 1 },
      { label: 'B', content: 'x = 1, x = 6', is_correct: false, sort: 2 },
      { label: 'C', content: 'x = -2, x = -3', is_correct: false, sort: 3 },
      { label: 'D', content: '无实数解', is_correct: false, sort: 4 }
    ]
  },
  {
    title: '英语语法选择',
    content: 'Choose the correct form of the verb in the sentence: "She _____ to school every day."',
    type: 'single',
    category: '英语',
    difficulty: 1,
    score: 3,
    status: 1,
    options: [
      { label: 'A', content: 'go', is_correct: false, sort: 1 },
      { label: 'B', content: 'goes', is_correct: true, sort: 2 },
      { label: 'C', content: 'going', is_correct: false, sort: 3 },
      { label: 'D', content: 'gone', is_correct: false, sort: 4 }
    ]
  },
  {
    title: '物理力学分析',
    content: '一个质量为2kg的物体在水平面上受到10N的水平力作用，摩擦系数为0.2，求物体的加速度。',
    type: 'single',
    category: '物理',
    difficulty: 3,
    score: 8,
    status: 1,
    options: [
      { label: 'A', content: '3.04 m/s²', is_correct: true, sort: 1 },
      { label: 'B', content: '5.0 m/s²', is_correct: false, sort: 2 },
      { label: 'C', content: '2.0 m/s²', is_correct: false, sort: 3 },
      { label: 'D', content: '4.0 m/s²', is_correct: false, sort: 4 }
    ]
  },
  {
    title: '化学反应方程式',
    content: '下列哪些是氧化还原反应？（多选题）',
    type: 'multiple',
    category: '化学',
    difficulty: 4,
    score: 10,
    status: 1,
    options: [
      { label: 'A', content: '2H₂ + O₂ → 2H₂O', is_correct: true, sort: 1 },
      { label: 'B', content: 'NaCl + AgNO₃ → AgCl + NaNO₃', is_correct: false, sort: 2 },
      { label: 'C', content: 'Zn + CuSO₄ → ZnSO₄ + Cu', is_correct: true, sort: 3 },
      { label: 'D', content: 'HCl + NaOH → NaCl + H₂O', is_correct: false, sort: 4 }
    ]
  },
  {
    title: '古诗词理解',
    content: '李白《静夜思》中"举头望明月，低头思故乡"表达了诗人怎样的情感？',
    type: 'single',
    category: '语文',
    difficulty: 2,
    score: 6,
    status: 1,
    options: [
      { label: 'A', content: '对家乡的思念之情', is_correct: true, sort: 1 },
      { label: 'B', content: '对月亮的赞美之情', is_correct: false, sort: 2 },
      { label: 'C', content: '对夜晚的恐惧之情', is_correct: false, sort: 3 },
      { label: 'D', content: '对未来的憧憬之情', is_correct: false, sort: 4 }
    ]
  }
]

// 创建题目
async function createQuestion(questionData) {
  try {
    const response = await axios.post(`${API_BASE_URL}/questions`, questionData)
    return response.data
  } catch (error) {
    console.error('创建题目失败:', error.response?.data || error.message)
    throw error
  }
}

// 快速生成数据
async function quickGenerate(count = 20) {
  console.log(`开始快速生成 ${count} 条数据...`)
  
  const createdQuestions = []
  
  for (let i = 0; i < count; i++) {
    try {
      // 选择模板并修改
      const template = QUESTION_TEMPLATES[i % QUESTION_TEMPLATES.length]
      const questionData = {
        ...template,
        title: `${template.title} - 第${i + 1}题`,
        sort: i + 1,
        created_by: 1,
        // 随机调整一些属性
        difficulty: Math.floor(Math.random() * 5) + 1,
        score: Math.floor(Math.random() * 10) + 1,
        status: Math.random() > 0.2 ? 1 : 0, // 80% 启用
      }

      console.log(`正在创建第 ${i + 1} 个题目: ${questionData.title}`)
      const result = await createQuestion(questionData)
      createdQuestions.push(result.data)
      
      // 小延迟
      await new Promise(resolve => setTimeout(resolve, 50))
      
    } catch (error) {
      console.error(`创建第 ${i + 1} 个题目失败:`, error.message)
      continue
    }
  }

  console.log(`\n✅ 成功生成 ${createdQuestions.length} 条数据！`)
  
  // 统计
  const stats = {
    types: {},
    categories: {},
    difficulties: {}
  }

  createdQuestions.forEach(q => {
    stats.types[q.type] = (stats.types[q.type] || 0) + 1
    stats.categories[q.category] = (stats.categories[q.category] || 0) + 1
    stats.difficulties[q.difficulty] = (stats.difficulties[q.difficulty] || 0) + 1
  })

  console.log('\n数据统计:')
  console.log('类型分布:', stats.types)
  console.log('分类分布:', stats.categories)
  console.log('难度分布:', stats.difficulties)
}

// 主函数
async function main() {
  const count = parseInt(process.argv[2]) || 20
  
  if (count <= 0 || count > 200) {
    console.error('请输入有效的数量 (1-200)')
    process.exit(1)
  }

  try {
    await quickGenerate(count)
  } catch (error) {
    console.error('生成失败:', error.message)
    process.exit(1)
  }
}

// 运行
if (require.main === module) {
  main()
}
