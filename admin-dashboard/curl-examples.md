# 直接使用 curl 命令生成数据

## 单个题目创建示例

### 数学题（单选）
```bash
curl -X POST http://localhost:3000/api/v1/questions \
  -H "Content-Type: application/json" \
  -d '{
    "title": "计算二次方程的解",
    "content": "求解方程 x² - 5x + 6 = 0 的所有实数解",
    "type": "single",
    "category": "数学",
    "difficulty": 2,
    "score": 5,
    "status": 1,
    "sort": 1,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "x = 2, x = 3", "is_correct": true, "sort": 1},
      {"label": "B", "content": "x = 1, x = 6", "is_correct": false, "sort": 2},
      {"label": "C", "content": "x = -2, x = -3", "is_correct": false, "sort": 3},
      {"label": "D", "content": "无实数解", "is_correct": false, "sort": 4}
    ]
  }'
```

### 英语题（单选）
```bash
curl -X POST http://localhost:3000/api/questions \
  -H "Content-Type: application/json" \
  -d '{
    "title": "英语语法选择",
    "content": "Choose the correct form: She _____ to school every day.",
    "type": "single",
    "category": "英语",
    "difficulty": 1,
    "score": 3,
    "status": 1,
    "sort": 2,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "go", "is_correct": false, "sort": 1},
      {"label": "B", "content": "goes", "is_correct": true, "sort": 2},
      {"label": "C", "content": "going", "is_correct": false, "sort": 3},
      {"label": "D", "content": "gone", "is_correct": false, "sort": 4}
    ]
  }'
```

### 化学题（多选）
```bash
curl -X POST http://localhost:3000/api/questions \
  -H "Content-Type: application/json" \
  -d '{
    "title": "化学反应方程式",
    "content": "下列哪些是氧化还原反应？",
    "type": "multiple",
    "category": "化学",
    "difficulty": 4,
    "score": 10,
    "status": 1,
    "sort": 3,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "2H₂ + O₂ → 2H₂O", "is_correct": true, "sort": 1},
      {"label": "B", "content": "NaCl + AgNO₃ → AgCl + NaNO₃", "is_correct": false, "sort": 2},
      {"label": "C", "content": "Zn + CuSO₄ → ZnSO₄ + Cu", "is_correct": true, "sort": 3},
      {"label": "D", "content": "HCl + NaOH → NaCl + H₂O", "is_correct": false, "sort": 4}
    ]
  }'
```

### 物理题（单选）
```bash
curl -X POST http://localhost:3000/api/questions \
  -H "Content-Type: application/json" \
  -d '{
    "title": "物理力学分析",
    "content": "一个质量为2kg的物体受到10N的力，根据牛顿第二定律F=ma，求物体的加速度",
    "type": "single",
    "category": "物理",
    "difficulty": 3,
    "score": 8,
    "status": 1,
    "sort": 4,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "5 m/s²", "is_correct": true, "sort": 1},
      {"label": "B", "content": "10 m/s²", "is_correct": false, "sort": 2},
      {"label": "C", "content": "2 m/s²", "is_correct": false, "sort": 3},
      {"label": "D", "content": "20 m/s²", "is_correct": false, "sort": 4}
    ]
  }'
```

### 语文题（单选）
```bash
curl -X POST http://localhost:3000/api/questions \
  -H "Content-Type: application/json" \
  -d '{
    "title": "古诗词理解",
    "content": "李白《静夜思》中"举头望明月，低头思故乡"表达了诗人怎样的情感？",
    "type": "single",
    "category": "语文",
    "difficulty": 2,
    "score": 6,
    "status": 1,
    "sort": 5,
    "created_by": 1,
    "options": [
      {"label": "A", "content": "对家乡的思念之情", "is_correct": true, "sort": 1},
      {"label": "B", "content": "对月亮的赞美之情", "is_correct": false, "sort": 2},
      {"label": "C", "content": "对夜晚的恐惧之情", "is_correct": false, "sort": 3},
      {"label": "D", "content": "对未来的憧憬之情", "is_correct": false, "sort": 4}
    ]
  }'
```

## 批量执行

### 方法一：使用 bash 脚本
```bash
# 给脚本执行权限
chmod +x generate-data.sh

# 生成 10 条数据（默认）
./generate-data.sh

# 生成 20 条数据
./generate-data.sh 20
```

### 方法二：一次性执行多个 curl 命令
```bash
# 创建 5 个题目
curl -X POST http://localhost:3000/api/questions -H "Content-Type: application/json" -d '{"title":"数学题1","content":"1+1=?","type":"single","category":"数学","difficulty":1,"score":2,"status":1,"sort":1,"created_by":1,"options":[{"label":"A","content":"1","is_correct":false,"sort":1},{"label":"B","content":"2","is_correct":true,"sort":2},{"label":"C","content":"3","is_correct":false,"sort":3},{"label":"D","content":"4","is_correct":false,"sort":4}]}' && \
curl -X POST http://localhost:3000/api/questions -H "Content-Type: application/json" -d '{"title":"英语题1","content":"What is your name?","type":"single","category":"英语","difficulty":1,"score":3,"status":1,"sort":2,"created_by":1,"options":[{"label":"A","content":"My name is Tom","is_correct":true,"sort":1},{"label":"B","content":"I am fine","is_correct":false,"sort":2},{"label":"C","content":"Thank you","is_correct":false,"sort":3},{"label":"D","content":"Goodbye","is_correct":false,"sort":4}]}' && \
curl -X POST http://localhost:3000/api/questions -H "Content-Type: application/json" -d '{"title":"物理题1","content":"光速是多少？","type":"single","category":"物理","difficulty":2,"score":5,"status":1,"sort":3,"created_by":1,"options":[{"label":"A","content":"3×10⁸ m/s","is_correct":true,"sort":1},{"label":"B","content":"3×10⁶ m/s","is_correct":false,"sort":2},{"label":"C","content":"3×10¹⁰ m/s","is_correct":false,"sort":3},{"label":"D","content":"3×10⁴ m/s","is_correct":false,"sort":4}]}' && \
curl -X POST http://localhost:3000/api/questions -H "Content-Type: application/json" -d '{"title":"化学题1","content":"水的化学式是？","type":"single","category":"化学","difficulty":1,"score":2,"status":1,"sort":4,"created_by":1,"options":[{"label":"A","content":"H₂O","is_correct":true,"sort":1},{"label":"B","content":"CO₂","is_correct":false,"sort":2},{"label":"C","content":"NaCl","is_correct":false,"sort":3},{"label":"D","content":"CaCO₃","is_correct":false,"sort":4}]}' && \
curl -X POST http://localhost:3000/api/questions -H "Content-Type: application/json" -d '{"title":"语文题1","content":"《红楼梦》的作者是？","type":"single","category":"语文","difficulty":2,"score":4,"status":1,"sort":5,"created_by":1,"options":[{"label":"A","content":"曹雪芹","is_correct":true,"sort":1},{"label":"B","content":"吴承恩","is_correct":false,"sort":2},{"label":"C","content":"施耐庵","is_correct":false,"sort":3},{"label":"D","content":"罗贯中","is_correct":false,"sort":4}]}'
```

## 查看创建的数据
```bash
# 获取所有题目
curl -X GET http://localhost:3000/api/questions

# 获取特定题目
curl -X GET http://localhost:3000/api/questions/1
```

## 清空数据
```bash
# 如果需要清空所有数据，先获取所有题目ID，然后逐个删除
# 或者直接重置数据库
```

## 使用建议

1. **快速测试**: 直接复制粘贴单个 curl 命令
2. **批量生成**: 使用 `generate-data.sh` 脚本
3. **自定义数据**: 修改 JSON 数据中的字段值
4. **检查结果**: 使用 GET 请求查看创建的数据

## 注意事项

- 确保后端服务运行在 `localhost:3000`
- 如果端口不同，修改 URL 中的端口号
- `created_by` 字段设置为 1（假设用户ID为1）
- `sort` 字段用于排序，建议递增设置
